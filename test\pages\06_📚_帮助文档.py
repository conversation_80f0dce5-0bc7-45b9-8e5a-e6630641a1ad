#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
帮助文档页面
提供使用指南和故障排除信息
"""

import streamlit as st

# 页面配置
st.set_page_config(
    page_title="帮助文档 - MediaCrawler",
    page_icon="📚",
    layout="wide"
)

def render_header():
    """渲染页面头部"""
    st.title("📚 帮助文档")
    st.markdown("---")
    
    st.markdown("""
    欢迎使用 MediaCrawler Web 测试工具！本页面提供详细的使用指南和故障排除信息。
    """)

def render_quick_start():
    """渲染快速入门"""
    st.subheader("🚀 快速入门")
    
    st.markdown("""
    ### 第一次使用
    
    1. **系统检查**
       - 点击左侧菜单的 "⚙️ 系统设置"
       - 在 "🔍 系统检查" 标签页中点击 "执行系统检查"
       - 确保所有检查项都显示 ✅
    
    2. **快速测试**
       - 点击左侧菜单的 "🔧 快速测试"
       - 选择要测试的平台（默认全选）
       - 点击 "开始测试" 按钮
       - 等待测试完成并查看结果
    
    3. **查看报告**
       - 点击左侧菜单的 "📋 测试报告"
       - 在报告列表中选择要查看的报告
       - 查看详细的测试结果和统计信息
    """)

def render_feature_guide():
    """渲染功能指南"""
    st.subheader("📖 功能指南")
    
    # 创建功能介绍的标签页
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🏠 首页", "🔧 快速测试", "📊 平台测试", "📋 测试报告", "⚙️ 系统设置"
    ])
    
    with tab1:
        st.markdown("""
        ### 🏠 首页仪表板
        
        首页提供系统的整体概览：
        
        - **系统状态概览**: 显示 Python 环境、依赖包、浏览器驱动和配置文件的状态
        - **平台状态**: 展示所有7个平台的可用性和最后测试时间
        - **快速操作**: 提供快速访问常用功能的按钮
        - **最近活动**: 显示最近的测试记录
        
        #### 使用技巧
        - 绿色指标表示正常，红色表示需要注意
        - 点击快速操作按钮可以直接跳转到对应功能
        - 系统状态会自动缓存，可以手动刷新页面更新
        """)
    
    with tab2:
        st.markdown("""
        ### 🔧 快速测试
        
        快速测试功能用于一键检查所有平台的基本功能：
        
        #### 测试配置
        - **平台选择**: 可以选择要测试的平台，支持全选/取消全选
        - **测试超时**: 设置单个平台的测试超时时间
        - **并发测试**: 启用后可以同时测试多个平台（实验性功能）
        - **详细日志**: 显示测试过程中的详细信息
        
        #### 测试内容
        - 模块导入检查
        - 爬虫实例创建
        - 必要方法验证（start、search、launch_browser）
        
        #### 结果解读
        - **通过**: 所有检查项都正常
        - **部分通过**: 部分功能可用，但可能缺少某些方法
        - **失败**: 无法正常工作，需要检查错误信息
        """)
    
    with tab3:
        st.markdown("""
        ### 📊 平台测试
        
        平台测试提供对单个平台的深度测试：
        
        #### 测试类型
        - **基础测试**: 模块导入、实例创建、方法检查
        - **高级测试**: 配置验证、性能测试
        
        #### 测试参数
        - **超时时间**: 单个测试的最大执行时间
        - **重试次数**: 失败时的重试次数
        - **详细输出**: 显示测试过程的详细日志
        
        #### 使用场景
        - 新平台开发后的功能验证
        - 问题诊断和调试
        - 性能基准测试
        """)
    
    with tab4:
        st.markdown("""
        ### 📋 测试报告
        
        测试报告功能提供完整的测试结果管理：
        
        #### 统计概览
        - 总报告数量和类型分布
        - 成功率趋势图表
        - 最近活动记录
        
        #### 报告列表
        - 支持按类型、状态、时间筛选
        - 可以查看报告的基本信息
        - 点击报告可以查看详细内容
        
        #### 报告详情
        - 完整的测试结果展示
        - 可视化图表分析
        - 支持下载和删除操作
        
        #### 导出功能
        - 批量导出为 CSV 格式
        - 清理旧报告功能
        """)
    
    with tab5:
        st.markdown("""
        ### ⚙️ 系统设置
        
        系统设置提供全面的配置管理：
        
        #### 测试设置
        - 默认超时时间、关键词、保存格式
        - 并发测试数量限制
        - 自动重试配置
        
        #### 平台设置
        - 单独配置每个平台的参数
        - 启用/禁用特定平台
        - 自定义测试关键词
        
        #### 界面设置
        - 主题选择（浅色/深色/自动）
        - 分页和显示选项
        - 调试信息开关
        
        #### 系统检查
        - 一键检查系统环境
        - 诊断常见问题
        
        #### 配置管理
        - 导出/导入配置文件
        - 重置为默认设置
        """)

def render_troubleshooting():
    """渲染故障排除"""
    st.subheader("🔧 故障排除")
    
    st.markdown("""
    ### 常见问题及解决方案
    """)
    
    # 使用 expander 组织问题
    with st.expander("❌ 系统检查失败"):
        st.markdown("""
        **问题**: 系统检查显示红色 ❌ 状态
        
        **可能原因**:
        1. Python 版本过低（需要 3.9+）
        2. 依赖包未安装或版本不兼容
        3. 浏览器驱动未安装
        4. 配置文件缺失
        
        **解决方案**:
        ```bash
        # 检查 Python 版本
        python --version
        
        # 重新安装依赖
        uv sync
        
        # 安装浏览器驱动
        uv run playwright install
        
        # 检查配置文件
        ls config/
        ```
        """)
    
    with st.expander("⚠️ 平台测试部分通过"):
        st.markdown("""
        **问题**: 平台测试显示 "部分通过" 状态
        
        **可能原因**:
        1. 平台爬虫类缺少必要方法
        2. 导入路径错误
        3. 类定义不完整
        
        **解决方案**:
        1. 检查爬虫类是否继承了 AbstractCrawler
        2. 确保实现了 start、search、launch_browser 方法
        3. 检查导入语句是否正确
        """)
    
    with st.expander("🐌 测试运行缓慢"):
        st.markdown("""
        **问题**: 测试执行时间过长
        
        **可能原因**:
        1. 网络连接问题
        2. 系统资源不足
        3. 超时设置过长
        
        **解决方案**:
        1. 检查网络连接
        2. 减少并发测试数量
        3. 调整超时时间设置
        4. 关闭其他占用资源的程序
        """)
    
    with st.expander("📄 报告无法生成"):
        st.markdown("""
        **问题**: 测试完成后无法生成报告
        
        **可能原因**:
        1. 磁盘空间不足
        2. 权限问题
        3. 报告目录不存在
        
        **解决方案**:
        1. 检查磁盘空间
        2. 确保有写入权限
        3. 手动创建 test/reports 目录
        """)
    
    with st.expander("🔄 页面无法加载"):
        st.markdown("""
        **问题**: Streamlit 页面无法正常加载
        
        **可能原因**:
        1. 端口被占用
        2. 防火墙阻止
        3. 浏览器缓存问题
        
        **解决方案**:
        1. 更换端口启动: `streamlit run streamlit_app.py --server.port 8502`
        2. 检查防火墙设置
        3. 清除浏览器缓存或使用无痕模式
        """)

def render_faq():
    """渲染常见问题"""
    st.subheader("❓ 常见问题")
    
    faqs = [
        {
            "question": "支持哪些平台？",
            "answer": "目前支持7个平台：小红书(xhs)、抖音(dy)、快手(ks)、B站(bili)、微博(wb)、贴吧(tieba)、知乎(zhihu)。"
        },
        {
            "question": "测试数据会保存在哪里？",
            "answer": "测试报告保存在 `test/reports/` 目录下，配置文件保存在 `test/config/` 目录下。"
        },
        {
            "question": "可以添加新的平台吗？",
            "answer": "可以。需要在 `media_platform/` 目录下添加新的平台模块，并在 `test_runner.py` 中注册。"
        },
        {
            "question": "如何备份测试配置？",
            "answer": "在 '⚙️ 系统设置' -> '📁 配置管理' 中可以导出当前配置为 JSON 文件。"
        },
        {
            "question": "测试失败后如何调试？",
            "answer": "可以在 '📊 平台测试' 中对单个平台进行详细测试，查看具体的错误信息和日志。"
        },
        {
            "question": "支持批量测试吗？",
            "answer": "支持。在 '🔧 快速测试' 中可以选择多个平台进行批量测试。"
        }
    ]
    
    for faq in faqs:
        with st.expander(f"Q: {faq['question']}"):
            st.write(f"A: {faq['answer']}")

def render_contact():
    """渲染联系信息"""
    st.subheader("📞 联系我们")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🐛 问题反馈
        
        如果您遇到问题或有改进建议，请通过以下方式联系我们：
        
        - **GitHub Issues**: 在项目仓库中提交 Issue
        - **邮件**: <EMAIL>
        - **文档**: 查看项目 README 文件
        """)
    
    with col2:
        st.markdown("""
        ### 📚 更多资源
        
        - **项目文档**: 查看完整的项目文档
        - **API 参考**: 查看 API 接口文档
        - **更新日志**: 了解最新功能和修复
        - **社区讨论**: 参与社区讨论
        """)
    
    st.markdown("""
    ---
    
    ### ⚖️ 使用声明
    
    **重要提醒**: MediaCrawler 仅供学习和研究使用，请遵守以下原则：
    
    1. 不得用于任何商业用途
    2. 使用时应遵守目标平台的使用条款和 robots.txt 规则
    3. 不得进行大规模爬取或对平台造成运营干扰
    4. 应合理控制请求频率，避免给目标平台带来不必要的负担
    5. 不得用于任何非法或不当的用途
    
    使用本工具即表示您同意遵守上述原则和相关法律法规。
    """)

def main():
    """主函数"""
    render_header()
    
    # 创建标签页
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🚀 快速入门",
        "📖 功能指南", 
        "🔧 故障排除",
        "❓ 常见问题",
        "📞 联系我们"
    ])
    
    with tab1:
        render_quick_start()
    
    with tab2:
        render_feature_guide()
    
    with tab3:
        render_troubleshooting()
    
    with tab4:
        render_faq()
    
    with tab5:
        render_contact()

if __name__ == "__main__":
    main()
