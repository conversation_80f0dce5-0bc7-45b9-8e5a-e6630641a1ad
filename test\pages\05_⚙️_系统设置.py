#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统设置页面
提供系统配置和环境管理功能
"""

import streamlit as st
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from test.utils.config_manager import ConfigManager
from test.utils.test_runner import TestRunner

# 页面配置
st.set_page_config(
    page_title="系统设置 - MediaCrawler",
    page_icon="⚙️",
    layout="wide"
)

def init_session_state():
    """初始化 session state"""
    if 'config_manager' not in st.session_state:
        st.session_state.config_manager = ConfigManager()
    
    if 'test_runner' not in st.session_state:
        st.session_state.test_runner = TestRunner()

def render_header():
    """渲染页面头部"""
    st.title("⚙️ 系统设置")
    st.markdown("---")
    
    st.markdown("""
    系统设置功能提供：
    - 🔧 测试参数配置
    - 🎯 平台设置管理
    - 🎨 界面偏好设置
    - 🔍 环境状态检查
    """)

def render_test_settings():
    """渲染测试设置"""
    st.subheader("🔧 测试设置")
    
    # 加载当前配置
    test_settings = st.session_state.config_manager.get_test_settings()
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 基础设置")
        
        default_timeout = st.slider(
            "默认超时时间 (秒)",
            min_value=10,
            max_value=300,
            value=test_settings.get("default_timeout", 30),
            step=5
        )
        
        default_keywords = st.text_input(
            "默认关键词",
            value=test_settings.get("default_keywords", "测试")
        )
        
        default_save_format = st.selectbox(
            "默认保存格式",
            options=["json", "csv", "db"],
            index=["json", "csv", "db"].index(test_settings.get("default_save_format", "json"))
        )
        
        default_login_type = st.selectbox(
            "默认登录方式",
            options=["qrcode", "phone", "cookie"],
            index=["qrcode", "phone", "cookie"].index(test_settings.get("default_login_type", "qrcode"))
        )
    
    with col2:
        st.markdown("#### 高级设置")
        
        enable_comments = st.checkbox(
            "默认启用评论爬取",
            value=test_settings.get("enable_comments", False)
        )
        
        max_concurrent_tests = st.slider(
            "最大并发测试数",
            min_value=1,
            max_value=10,
            value=test_settings.get("max_concurrent_tests", 3)
        )
        
        auto_retry = st.checkbox(
            "自动重试失败的测试",
            value=test_settings.get("auto_retry", False)
        )
        
        retry_count = st.slider(
            "重试次数",
            min_value=0,
            max_value=5,
            value=test_settings.get("retry_count", 1),
            disabled=not auto_retry
        )
    
    # 保存按钮
    if st.button("保存测试设置", type="primary"):
        new_settings = {
            "default_timeout": default_timeout,
            "default_keywords": default_keywords,
            "default_save_format": default_save_format,
            "default_login_type": default_login_type,
            "enable_comments": enable_comments,
            "max_concurrent_tests": max_concurrent_tests,
            "auto_retry": auto_retry,
            "retry_count": retry_count
        }
        
        if st.session_state.config_manager.update_test_settings(new_settings):
            st.success("测试设置已保存")
        else:
            st.error("保存失败")

def render_platform_settings():
    """渲染平台设置"""
    st.subheader("🎯 平台设置")
    
    platforms = st.session_state.test_runner.platforms
    
    # 平台选择
    platform_keys = list(platforms.keys())
    selected_platform = st.selectbox(
        "选择平台",
        options=platform_keys,
        format_func=lambda x: f"{platforms[x]['icon']} {platforms[x]['name']} ({x})"
    )
    
    if selected_platform:
        platform_info = platforms[selected_platform]
        platform_settings = st.session_state.config_manager.get_platform_settings(selected_platform)
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown(f"#### {platform_info['name']} 设置")
            
            enabled = st.checkbox(
                "启用此平台",
                value=platform_settings.get("enabled", True)
            )
            
            timeout = st.slider(
                "超时时间 (秒)",
                min_value=10,
                max_value=300,
                value=platform_settings.get("timeout", 30),
                step=5
            )
            
            # 默认关键词设置
            st.markdown("**默认关键词**")
            current_keywords = platform_settings.get("default_keywords", platform_info["test_keywords"])
            
            keywords_text = st.text_area(
                "关键词 (每行一个)",
                value="\n".join(current_keywords),
                height=100
            )
            
            new_keywords = [k.strip() for k in keywords_text.split("\n") if k.strip()]
        
        with col2:
            st.markdown("#### 平台信息")
            
            st.info(f"**平台名称**: {platform_info['name']}")
            st.info(f"**平台代码**: {selected_platform}")
            st.info(f"**图标**: {platform_info['icon']}")
            
            # 显示当前状态
            platform_status = st.session_state.test_runner.get_platform_status()
            status = platform_status.get(selected_platform, {})
            
            if status.get("available", False):
                st.success("✅ 平台可用")
            else:
                st.error("❌ 平台不可用")
            
            st.write("**方法检查**:")
            methods = status.get("methods", {})
            for method, available in methods.items():
                icon = "✅" if available else "❌"
                st.write(f"- {method}: {icon}")
        
        # 保存平台设置
        if st.button(f"保存 {platform_info['name']} 设置", type="primary"):
            new_platform_settings = {
                "enabled": enabled,
                "timeout": timeout,
                "default_keywords": new_keywords
            }
            
            if st.session_state.config_manager.update_platform_settings(selected_platform, new_platform_settings):
                st.success(f"{platform_info['name']} 设置已保存")
            else:
                st.error("保存失败")

def render_ui_settings():
    """渲染界面设置"""
    st.subheader("🎨 界面设置")
    
    ui_settings = st.session_state.config_manager.get_ui_settings()
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 显示设置")
        
        theme = st.selectbox(
            "主题",
            options=["light", "dark", "auto"],
            index=["light", "dark", "auto"].index(ui_settings.get("theme", "light"))
        )
        
        auto_refresh = st.checkbox(
            "自动刷新",
            value=ui_settings.get("auto_refresh", True)
        )
        
        show_debug_info = st.checkbox(
            "显示调试信息",
            value=ui_settings.get("show_debug_info", False)
        )
    
    with col2:
        st.markdown("#### 分页设置")
        
        items_per_page = st.slider(
            "每页显示项目数",
            min_value=5,
            max_value=50,
            value=ui_settings.get("items_per_page", 10),
            step=5
        )
        
        compact_mode = st.checkbox(
            "紧凑模式",
            value=ui_settings.get("compact_mode", False)
        )
        
        show_tooltips = st.checkbox(
            "显示工具提示",
            value=ui_settings.get("show_tooltips", True)
        )
    
    # 保存界面设置
    if st.button("保存界面设置", type="primary"):
        new_ui_settings = {
            "theme": theme,
            "auto_refresh": auto_refresh,
            "show_debug_info": show_debug_info,
            "items_per_page": items_per_page,
            "compact_mode": compact_mode,
            "show_tooltips": show_tooltips
        }
        
        if st.session_state.config_manager.update_ui_settings(new_ui_settings):
            st.success("界面设置已保存")
        else:
            st.error("保存失败")

def render_system_check():
    """渲染系统检查"""
    st.subheader("🔍 系统环境检查")
    
    if st.button("执行系统检查", type="primary"):
        with st.spinner("正在检查系统环境..."):
            system_status = st.session_state.test_runner.check_system_status()
        
        st.markdown("#### 检查结果")
        
        # Python 环境
        python_icon = "✅" if system_status.get("python_ok", False) else "❌"
        st.write(f"{python_icon} **Python 环境**: {system_status.get('python_version', '未知')}")
        
        # 依赖包
        deps_icon = "✅" if system_status.get("dependencies_ok", False) else "❌"
        st.write(f"{deps_icon} **依赖包**: {system_status.get('deps_count', 0)} 个包")
        
        # 浏览器驱动
        browser_icon = "✅" if system_status.get("browser_ok", False) else "❌"
        st.write(f"{browser_icon} **浏览器驱动**: {system_status.get('browser_count', 0)} 个驱动")
        
        # 配置文件
        config_icon = "✅" if system_status.get("config_ok", False) else "❌"
        st.write(f"{config_icon} **配置文件**: {'完整' if system_status.get('config_ok', False) else '缺失'}")
        
        # 总体状态
        all_ok = all([
            system_status.get("python_ok", False),
            system_status.get("dependencies_ok", False),
            system_status.get("config_ok", False)
        ])
        
        if all_ok:
            st.success("🎉 系统环境检查通过！")
        else:
            st.warning("⚠️ 系统环境存在问题，请检查上述项目")

def render_config_management():
    """渲染配置管理"""
    st.subheader("📁 配置管理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 导出配置")
        
        if st.button("导出当前配置", use_container_width=True):
            config_json = st.session_state.config_manager.export_config()
            
            st.download_button(
                label="下载配置文件",
                data=config_json,
                file_name=f"mediacrawler_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
    
    with col2:
        st.markdown("#### 导入配置")
        
        uploaded_file = st.file_uploader(
            "选择配置文件",
            type=["json"],
            help="上传之前导出的配置文件"
        )
        
        if uploaded_file is not None:
            try:
                config_content = uploaded_file.read().decode('utf-8')
                
                if st.button("导入配置", use_container_width=True):
                    if st.session_state.config_manager.import_config(config_content):
                        st.success("配置导入成功")
                    else:
                        st.error("配置导入失败")
            
            except Exception as e:
                st.error(f"文件读取失败: {str(e)}")
    
    # 重置配置
    st.markdown("#### ⚠️ 危险操作")
    
    if st.button("重置所有配置", type="secondary", use_container_width=True):
        if st.session_state.config_manager.reset_config():
            st.success("配置已重置为默认值")
        else:
            st.error("重置失败")

def main():
    """主函数"""
    init_session_state()
    
    render_header()
    
    # 创建标签页
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🔧 测试设置", 
        "🎯 平台设置", 
        "🎨 界面设置", 
        "🔍 系统检查", 
        "📁 配置管理"
    ])
    
    with tab1:
        render_test_settings()
    
    with tab2:
        render_platform_settings()
    
    with tab3:
        render_ui_settings()
    
    with tab4:
        render_system_check()
    
    with tab5:
        render_config_management()

if __name__ == "__main__":
    main()
