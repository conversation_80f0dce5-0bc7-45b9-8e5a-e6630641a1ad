#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MediaCrawler 快速測試腳本
快速驗證所有平台的基本功能
"""

import sys
import os
import subprocess
from datetime import datetime

# 添加專案根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def print_header(title: str):
    """列印標題"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)


def test_platform_help(platform: str) -> bool:
    """測試平台的 help 命令"""
    try:
        cmd = [sys.executable, "main.py", "--platform", platform, "--help"]
        result = subprocess.run(
            cmd,
            cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0
    except Exception:
        return False


def test_platform_import() -> dict:
    """測試平台模組導入"""
    results = {}
    
    platforms = {
        "xhs": "media_platform.xhs.XiaoHongShuCrawler",
        "dy": "media_platform.douyin.DouYinCrawler", 
        "ks": "media_platform.kuaishou.KuaishouCrawler",
        "bili": "media_platform.bilibili.BilibiliCrawler",
        "wb": "media_platform.weibo.WeiboCrawler",
        "tieba": "media_platform.tieba.TieBaCrawler",
        "zhihu": "media_platform.zhihu.ZhihuCrawler"
    }
    
    for platform, module_path in platforms.items():
        try:
            module_name, class_name = module_path.rsplit('.', 1)
            module = __import__(module_name, fromlist=[class_name])
            crawler_class = getattr(module, class_name)
            
            # 嘗試創建實例
            crawler = crawler_class()
            
            # 檢查必要方法
            has_start = hasattr(crawler, 'start')
            has_search = hasattr(crawler, 'search')
            has_launch_browser = hasattr(crawler, 'launch_browser')
            
            results[platform] = {
                "import_success": True,
                "instance_created": True,
                "has_start": has_start,
                "has_search": has_search,
                "has_launch_browser": has_launch_browser,
                "all_methods": has_start and has_search and has_launch_browser
            }
            
        except Exception as e:
            results[platform] = {
                "import_success": False,
                "error": str(e),
                "instance_created": False,
                "has_start": False,
                "has_search": False,
                "has_launch_browser": False,
                "all_methods": False
            }
    
    return results


def main():
    """主函數"""
    print_header("MediaCrawler 快速測試")
    
    print("🔧 正在測試平台模組導入...")
    
    # 測試導入
    import_results = test_platform_import()
    
    print("\n📊 測試結果:")
    print("-" * 60)
    
    platform_names = {
        "xhs": "小紅書",
        "dy": "抖音",
        "ks": "快手", 
        "bili": "B站",
        "wb": "微博",
        "tieba": "貼吧",
        "zhihu": "知乎"
    }
    
    success_count = 0
    total_count = len(import_results)
    
    for platform, result in import_results.items():
        name = platform_names.get(platform, platform)
        
        if result["all_methods"]:
            status = "✅ 通過"
            success_count += 1
        else:
            status = "❌ 失敗"
        
        print(f"{status} {name} ({platform})")
        
        if not result["import_success"]:
            print(f"    ❌ 導入失敗: {result.get('error', '未知錯誤')}")
        elif not result["instance_created"]:
            print(f"    ❌ 實例創建失敗")
        else:
            missing_methods = []
            if not result["has_start"]:
                missing_methods.append("start")
            if not result["has_search"]:
                missing_methods.append("search")
            if not result["has_launch_browser"]:
                missing_methods.append("launch_browser")
            
            if missing_methods:
                print(f"    ⚠️  缺少方法: {', '.join(missing_methods)}")
            else:
                print(f"    ✅ 所有必要方法都存在")
    
    print("-" * 60)
    print(f"📈 總體結果: {success_count}/{total_count} 平台通過測試")
    
    if success_count == total_count:
        print("🎉 所有平台測試通過！可以開始使用爬蟲功能。")
    else:
        print("⚠️  部分平台測試失敗，請檢查相關模組。")
    
    # 保存測試報告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"test/quick_test_report_{timestamp}.txt"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("MediaCrawler 快速測試報告\n")
            f.write("=" * 40 + "\n")
            f.write(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for platform, result in import_results.items():
                name = platform_names.get(platform, platform)
                f.write(f"平台: {name} ({platform})\n")
                f.write(f"導入成功: {result['import_success']}\n")
                f.write(f"實例創建: {result['instance_created']}\n")
                f.write(f"start方法: {result['has_start']}\n")
                f.write(f"search方法: {result['has_search']}\n")
                f.write(f"launch_browser方法: {result['has_launch_browser']}\n")
                f.write(f"整體狀態: {'通過' if result['all_methods'] else '失敗'}\n")
                if not result['import_success']:
                    f.write(f"錯誤: {result.get('error', '未知錯誤')}\n")
                f.write("-" * 30 + "\n")
            
            f.write(f"\n總體結果: {success_count}/{total_count} 平台通過\n")
        
        print(f"\n📄 測試報告已保存: {report_file}")
        
    except Exception as e:
        print(f"⚠️  保存測試報告失敗: {str(e)}")
    
    print("\n🏁 快速測試完成！")
    
    # 提供使用建議
    print("\n💡 使用建議:")
    if success_count == total_count:
        print("   所有平台都可以正常使用，您可以:")
        print("   1. 運行 'uv run python main.py --help' 查看完整使用說明")
        print("   2. 選擇一個平台開始爬取，例如:")
        print("      uv run python main.py --platform xhs --lt qrcode --type search --keywords '美食'")
        print("   3. 運行 'uv run python test/run_tests.py' 進行更詳細的測試")
    else:
        print("   部分平台存在問題，建議:")
        print("   1. 檢查依賴是否完整安裝: uv sync")
        print("   2. 檢查 Python 版本是否符合要求 (3.9+)")
        print("   3. 查看錯誤詳情並修復相關問題")


if __name__ == "__main__":
    main()
