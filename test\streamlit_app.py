#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MediaCrawler Streamlit Web 测试工具
主应用入口文件
"""

import streamlit as st
import sys
import os
from datetime import datetime
import json

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# 确保工作目录正确
original_cwd = os.getcwd()
os.chdir(project_root)

# 导入工具模块
from test.utils.test_runner import TestRunner
from test.utils.config_manager import ConfigManager
from test.utils.report_manager import ReportManager

# 页面配置
st.set_page_config(
    page_title="MediaCrawler 测试工具",
    page_icon="🕷️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义 CSS 样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .status-card {
        background: linear-gradient(90deg, #f0f2f6 0%, #ffffff 100%);
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #1f77b4;
        margin: 1rem 0;
    }
    
    .success-card {
        border-left-color: #28a745;
        background: linear-gradient(90deg, #d4edda 0%, #ffffff 100%);
    }
    
    .warning-card {
        border-left-color: #ffc107;
        background: linear-gradient(90deg, #fff3cd 0%, #ffffff 100%);
    }
    
    .error-card {
        border-left-color: #dc3545;
        background: linear-gradient(90deg, #f8d7da 0%, #ffffff 100%);
    }
    
    .metric-container {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .platform-status {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem;
        margin: 0.25rem 0;
        border-radius: 5px;
        background: #f8f9fa;
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """初始化 session state"""
    if 'test_runner' not in st.session_state:
        st.session_state.test_runner = TestRunner()
    
    if 'config_manager' not in st.session_state:
        st.session_state.config_manager = ConfigManager()
    
    if 'report_manager' not in st.session_state:
        st.session_state.report_manager = ReportManager()
    
    if 'last_test_results' not in st.session_state:
        st.session_state.last_test_results = None
    
    if 'system_status' not in st.session_state:
        st.session_state.system_status = None

def load_system_status():
    """加载系统状态"""
    if st.session_state.system_status is None:
        with st.spinner("正在检查系统状态..."):
            st.session_state.system_status = st.session_state.test_runner.check_system_status()
    return st.session_state.system_status

def render_header():
    """渲染页面头部"""
    st.markdown('<h1 class="main-header">🕷️ MediaCrawler 测试工具</h1>', unsafe_allow_html=True)
    
    # 添加导航提示
    st.markdown("""
    <div style="text-align: center; color: #666; margin-bottom: 2rem;">
        欢迎使用 MediaCrawler Web 测试工具！请从左侧边栏选择功能模块。
    </div>
    """, unsafe_allow_html=True)

def render_system_overview():
    """渲染系统概览"""
    st.subheader("📊 系统状态概览")
    
    system_status = load_system_status()
    
    # 创建状态指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        python_status = "✅" if system_status.get("python_ok", False) else "❌"
        st.metric(
            label="Python 环境",
            value=python_status,
            delta=system_status.get("python_version", "未知")
        )
    
    with col2:
        deps_status = "✅" if system_status.get("dependencies_ok", False) else "❌"
        st.metric(
            label="依赖包",
            value=deps_status,
            delta=f"{system_status.get('deps_count', 0)} 个包"
        )
    
    with col3:
        browser_status = "✅" if system_status.get("browser_ok", False) else "❌"
        st.metric(
            label="浏览器驱动",
            value=browser_status,
            delta=f"{system_status.get('browser_count', 0)} 个驱动"
        )
    
    with col4:
        config_status = "✅" if system_status.get("config_ok", False) else "❌"
        st.metric(
            label="配置文件",
            value=config_status,
            delta="完整" if system_status.get("config_ok", False) else "缺失"
        )

def render_platform_status():
    """渲染平台状态"""
    st.subheader("🎯 平台状态")
    
    platforms = {
        "xhs": {"name": "小红书", "icon": "📱"},
        "dy": {"name": "抖音", "icon": "🎵"},
        "ks": {"name": "快手", "icon": "⚡"},
        "bili": {"name": "B站", "icon": "📺"},
        "wb": {"name": "微博", "icon": "🐦"},
        "tieba": {"name": "贴吧", "icon": "💬"},
        "zhihu": {"name": "知乎", "icon": "🧠"}
    }
    
    # 获取平台状态
    platform_status = st.session_state.test_runner.get_platform_status()
    
    # 创建两列布局
    col1, col2 = st.columns(2)
    
    platform_items = list(platforms.items())
    mid_point = len(platform_items) // 2 + 1
    
    with col1:
        for platform_key, platform_info in platform_items[:mid_point]:
            status = platform_status.get(platform_key, {})
            status_icon = "✅" if status.get("available", False) else "❌"
            last_test = status.get("last_test", "从未测试")
            
            st.markdown(f"""
            <div class="platform-status">
                <span>{platform_info['icon']} {platform_info['name']}</span>
                <span>{status_icon} {last_test}</span>
            </div>
            """, unsafe_allow_html=True)
    
    with col2:
        for platform_key, platform_info in platform_items[mid_point:]:
            status = platform_status.get(platform_key, {})
            status_icon = "✅" if status.get("available", False) else "❌"
            last_test = status.get("last_test", "从未测试")
            
            st.markdown(f"""
            <div class="platform-status">
                <span>{platform_info['icon']} {platform_info['name']}</span>
                <span>{status_icon} {last_test}</span>
            </div>
            """, unsafe_allow_html=True)

def render_quick_actions():
    """渲染快速操作"""
    st.subheader("🚀 快速操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔧 快速测试", use_container_width=True, type="primary"):
            st.switch_page("pages/01_🔧_快速测试.py")
    
    with col2:
        if st.button("📊 平台测试", use_container_width=True):
            st.switch_page("pages/02_📊_平台测试.py")
    
    with col3:
        if st.button("📋 查看报告", use_container_width=True):
            st.switch_page("pages/04_📋_测试报告.py")

def render_recent_activity():
    """渲染最近活动"""
    st.subheader("📈 最近活动")
    
    # 获取最近的测试报告
    recent_reports = st.session_state.report_manager.get_recent_reports(limit=5)
    
    if recent_reports:
        for report in recent_reports:
            with st.expander(f"📄 {report['name']} - {report['date']}"):
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.write(f"**类型**: {report['type']}")
                with col2:
                    st.write(f"**状态**: {report['status']}")
                with col3:
                    st.write(f"**平台数**: {report['platform_count']}")
    else:
        st.info("暂无测试记录，开始您的第一次测试吧！")

def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.title("🕷️ MediaCrawler")
        st.markdown("---")
        
        # 系统状态简要显示
        system_status = load_system_status()
        overall_status = "🟢 正常" if all([
            system_status.get("python_ok", False),
            system_status.get("dependencies_ok", False),
            system_status.get("config_ok", False)
        ]) else "🟡 需要检查"
        
        st.markdown(f"**系统状态**: {overall_status}")
        
        # 导航菜单
        st.markdown("### 📋 功能菜单")
        
        menu_items = [
            ("🏠", "首页", "streamlit_app.py"),
            ("🔧", "快速测试", "pages/01_🔧_快速测试.py"),
            ("📊", "平台测试", "pages/02_📊_平台测试.py"),
            ("🚀", "实际爬取", "pages/03_🚀_实际爬取.py"),
            ("📋", "测试报告", "pages/04_📋_测试报告.py"),
            ("⚙️", "系统设置", "pages/05_⚙️_系统设置.py"),
            ("📚", "帮助文档", "pages/06_📚_帮助文档.py")
        ]
        
        for icon, name, page in menu_items:
            if st.button(f"{icon} {name}", use_container_width=True):
                if page != "streamlit_app.py":
                    st.switch_page(page)
        
        st.markdown("---")
        st.markdown("### ℹ️ 关于")
        st.markdown("""
        **版本**: v1.0.0  
        **更新**: 2025-07-09  
        **作者**: MediaCrawler Team
        """)

def main():
    """主函数"""
    # 初始化
    init_session_state()
    
    # 渲染侧边栏
    render_sidebar()
    
    # 渲染主内容
    render_header()
    
    # 创建主要内容区域
    tab1, tab2, tab3 = st.tabs(["📊 系统概览", "🎯 平台状态", "📈 最近活动"])
    
    with tab1:
        render_system_overview()
        render_quick_actions()
    
    with tab2:
        render_platform_status()
    
    with tab3:
        render_recent_activity()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.8rem;">
        MediaCrawler Web 测试工具 | 仅供学习和研究使用 | 请遵守平台使用条款
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
