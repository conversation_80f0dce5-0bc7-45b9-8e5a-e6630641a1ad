#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MediaCrawler 測試總結腳本
展示所有可用的測試工具和使用方法
"""

import os
import sys
from datetime import datetime

def print_header(title: str):
    """列印標題"""
    print("\n" + "="*70)
    print(f"  {title}")
    print("="*70)

def print_section(title: str):
    """列印章節標題"""
    print(f"\n📋 {title}")
    print("-" * 50)

def main():
    """主函數"""
    print_header("MediaCrawler 測試工具總結")
    
    print("🎯 本專案已為您準備了完整的測試工具套件，用於測試所有7個平台的爬蟲功能。")
    
    print_section("📁 測試文件結構")
    
    test_files = {
        "🚀 run_tests.py": "主測試運行器 - 提供統一的測試介面 (推薦使用)",
        "⚡ quick_test.py": "快速測試 - 驗證所有平台的基本功能",
        "🔧 test_platform_crawlers.py": "基礎功能測試 - 測試平台初始化",
        "📊 test_platform_crawl.py": "爬取功能測試 - 測試實際數據爬取",
        "🌐 test_real_crawl.py": "真實爬取測試 - 使用 main.py 進行測試",
        "📖 README.md": "詳細說明文檔 - 包含所有使用方法"
    }
    
    for file, description in test_files.items():
        print(f"   {file:<30} {description}")
    
    print_section("🎯 支援的平台")
    
    platforms = [
        ("xhs", "小紅書", "✅"),
        ("dy", "抖音", "✅"),
        ("ks", "快手", "✅"),
        ("bili", "B站", "✅"),
        ("wb", "微博", "✅"),
        ("tieba", "貼吧", "✅"),
        ("zhihu", "知乎", "✅")
    ]
    
    for code, name, status in platforms:
        print(f"   {status} {name:<8} ({code})")
    
    print_section("🚀 快速開始")
    
    print("1. 🔥 推薦方式 - 使用主測試運行器:")
    print("   uv run python test/run_tests.py")
    print("   (提供互動式選單，包含所有測試功能)")
    
    print("\n2. ⚡ 快速驗證 - 檢查所有平台:")
    print("   uv run python test/quick_test.py")
    print("   (快速檢查所有平台的基本功能)")
    
    print("\n3. 🔧 基礎測試 - 平台初始化:")
    print("   uv run python test/test_platform_crawlers.py")
    print("   (測試所有平台的基本屬性和方法)")
    
    print_section("📊 測試類型說明")
    
    test_types = [
        ("基礎功能測試", "檢查平台模組導入和基本方法", "不需要登錄", "快速"),
        ("爬取功能測試", "測試實際的數據爬取流程", "需要掃碼登錄", "完整"),
        ("真實爬取測試", "使用 main.py 進行端到端測試", "需要掃碼登錄", "真實"),
        ("系統環境檢查", "檢查依賴、配置和瀏覽器驅動", "不需要登錄", "診斷")
    ]
    
    for test_type, description, login_req, level in test_types:
        print(f"   📝 {test_type:<12} {description}")
        print(f"      登錄要求: {login_req:<10} 測試級別: {level}")
        print()
    
    print_section("💡 使用建議")
    
    suggestions = [
        "首次使用建議運行 quick_test.py 進行快速驗證",
        "日常測試使用 run_tests.py 的互動式介面",
        "如需測試實際爬取功能，準備好對應平台的賬號",
        "測試報告會自動保存在 test/ 目錄下",
        "遇到問題可查看 test/README.md 獲取詳細說明"
    ]
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"   {i}. {suggestion}")
    
    print_section("🔍 測試報告")
    
    # 查找現有的測試報告
    test_dir = os.path.dirname(os.path.abspath(__file__))
    report_files = []
    
    for file in os.listdir(test_dir):
        if file.endswith('_report_') and '.txt' in file:
            report_files.append(file)
    
    if report_files:
        print(f"   📄 已生成 {len(report_files)} 個測試報告:")
        for report in sorted(report_files)[-5:]:  # 顯示最近5個
            print(f"      {report}")
        if len(report_files) > 5:
            print(f"      ... 還有 {len(report_files) - 5} 個報告")
    else:
        print("   📭 尚未生成測試報告，運行測試後會自動創建")
    
    print_section("⚙️ 環境要求")
    
    requirements = [
        "Python 3.9+ ✅",
        "已安裝依賴 (uv sync) ✅", 
        "已安裝瀏覽器驅動 (uv run playwright install) ✅",
        "網路連接正常 ⚠️",
        "對應平台賬號 (用於登錄測試) ⚠️"
    ]
    
    for req in requirements:
        print(f"   {req}")
    
    print_section("🎉 下一步")
    
    next_steps = [
        "運行 'uv run python test/quick_test.py' 進行快速驗證",
        "如果快速測試通過，可以開始使用爬蟲功能",
        "選擇一個平台進行實際測試，例如:",
        "  uv run python main.py --platform xhs --lt qrcode --type search --keywords '美食'",
        "查看 test/README.md 獲取更多詳細說明"
    ]
    
    for i, step in enumerate(next_steps, 1):
        if step.startswith("  "):
            print(f"     {step}")
        else:
            print(f"   {i}. {step}")
    
    print_header("測試工具準備完成")
    
    print("🎊 恭喜！MediaCrawler 測試環境已完全準備就緒！")
    print("📚 所有測試工具都已創建並可以正常使用。")
    print("🚀 您現在可以開始測試和使用 MediaCrawler 的強大功能了！")
    
    print(f"\n📅 測試工具創建時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("💝 祝您使用愉快！")


if __name__ == "__main__":
    main()
