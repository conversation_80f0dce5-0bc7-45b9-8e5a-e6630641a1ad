# MediaCrawler Streamlit Web 测试工具

## 🎯 简介

MediaCrawler Streamlit Web 测试工具是一个现代化的 Web 界面，用于测试和管理 MediaCrawler 项目的7个平台爬虫功能。通过直观的图形界面，您可以轻松进行平台测试、查看报告和管理配置。

## ✨ 主要功能

- 🏠 **首页仪表板** - 系统状态概览和快速导航
- 🔧 **快速测试** - 一键测试所有平台基础功能
- 📊 **平台测试** - 单个平台的详细测试和诊断
- 🚀 **实际爬取** - 真实环境的爬取测试（需登录）
- 📋 **测试报告** - 可视化报告管理和分析
- ⚙️ **系统设置** - 配置管理和环境检查
- 📚 **帮助文档** - 完整的使用指南

## 🚀 快速开始

### 1. 环境要求

- Python 3.9+
- 已安装 MediaCrawler 项目依赖
- 已安装 Streamlit 相关依赖

### 2. 安装依赖

```bash
# 安装 Streamlit 相关依赖
pip install streamlit plotly pandas

# 或者使用 uv（推荐）
uv add streamlit plotly pandas
```

### 3. 启动应用

#### 方法一：使用启动脚本（推荐）

```bash
# 进入测试目录
cd test

# 使用启动脚本
python run_streamlit.py

# 自定义端口和主机
python run_streamlit.py --port 8502 --host 0.0.0.0

# 开发模式（启用自动重载）
python run_streamlit.py --dev
```

#### 方法二：直接使用 Streamlit

```bash
# 进入测试目录
cd test

# 启动应用
streamlit run streamlit_app.py

# 自定义配置
streamlit run streamlit_app.py --server.port 8502
```

### 4. 访问应用

启动成功后，在浏览器中访问：
- 默认地址: http://localhost:8501
- 自定义地址: http://localhost:端口号

## 📖 使用指南

### 首次使用

1. **系统检查**
   - 访问 "⚙️ 系统设置" → "🔍 系统检查"
   - 点击 "执行系统检查" 确保环境正常

2. **快速测试**
   - 访问 "🔧 快速测试"
   - 选择要测试的平台
   - 点击 "开始测试" 查看结果

3. **查看报告**
   - 访问 "📋 测试报告"
   - 查看测试历史和统计信息

### 功能详解

#### 🔧 快速测试
- **用途**: 批量测试所有平台的基础功能
- **测试内容**: 模块导入、实例创建、方法检查
- **特点**: 无需登录，快速验证

#### 📊 平台测试
- **用途**: 单个平台的深度测试和诊断
- **测试类型**: 基础测试、高级测试、性能测试
- **特点**: 详细的错误信息和调试日志

#### 🚀 实际爬取
- **用途**: 真实环境的爬取功能测试
- **要求**: 需要登录对应平台账号
- **注意**: 请遵守平台使用条款

#### 📋 测试报告
- **功能**: 报告管理、统计分析、趋势图表
- **格式**: 支持 JSON、CSV 导出
- **筛选**: 按类型、状态、时间筛选

#### ⚙️ 系统设置
- **配置**: 测试参数、平台设置、界面偏好
- **管理**: 配置导入导出、环境检查
- **备份**: 配置文件备份和恢复

## 🔧 配置说明

### 配置文件位置

- **应用配置**: `test/config/streamlit_config.json`
- **测试报告**: `test/reports/`
- **日志文件**: `test/logs/`

### 主要配置项

```json
{
  "test_settings": {
    "default_timeout": 30,
    "default_keywords": "测试",
    "default_save_format": "json",
    "max_concurrent_tests": 3
  },
  "platform_settings": {
    "xhs": {
      "enabled": true,
      "default_keywords": ["美食", "旅行"],
      "timeout": 30
    }
  },
  "ui_settings": {
    "theme": "light",
    "items_per_page": 10
  }
}
```

## 🎨 界面预览

### 首页仪表板
- 系统状态卡片
- 平台状态概览
- 快速操作按钮
- 最近活动记录

### 测试页面
- 实时进度显示
- 详细日志输出
- 结果可视化
- 错误诊断信息

### 报告页面
- 统计图表
- 趋势分析
- 报告列表
- 详情查看

## 🔍 故障排除

### 常见问题

1. **应用无法启动**
   ```bash
   # 检查依赖
   pip list | grep streamlit
   
   # 重新安装
   pip install --upgrade streamlit
   ```

2. **页面加载缓慢**
   - 检查网络连接
   - 清除浏览器缓存
   - 尝试无痕模式

3. **测试失败**
   - 检查 MediaCrawler 环境
   - 查看系统设置中的环境检查
   - 参考帮助文档

4. **端口被占用**
   ```bash
   # 使用其他端口
   python run_streamlit.py --port 8502
   ```

### 调试模式

```bash
# 启用详细日志
streamlit run streamlit_app.py --logger.level debug

# 开发模式
python run_streamlit.py --dev
```

## 📊 性能优化

### 建议配置

- **内存**: 建议 4GB+ RAM
- **CPU**: 多核处理器
- **网络**: 稳定的网络连接
- **浏览器**: Chrome、Firefox、Edge 最新版本

### 优化设置

1. **减少并发测试数量**
2. **调整超时时间**
3. **关闭不必要的功能**
4. **定期清理测试报告**

## 🤝 贡献指南

### 开发环境

```bash
# 克隆项目
git clone <repository-url>

# 安装开发依赖
pip install -r requirements-dev.txt

# 启动开发模式
python run_streamlit.py --dev
```

### 代码结构

```
test/
├── streamlit_app.py          # 主应用
├── pages/                    # 页面模块
│   ├── 01_🔧_快速测试.py
│   ├── 02_📊_平台测试.py
│   └── ...
├── utils/                    # 工具模块
│   ├── test_runner.py
│   ├── config_manager.py
│   └── report_manager.py
└── config/                   # 配置文件
```

### 添加新功能

1. 在 `pages/` 目录下创建新页面
2. 在 `utils/` 目录下添加工具函数
3. 更新主应用的导航菜单
4. 添加相应的测试和文档

## 📄 许可证

本项目遵循与 MediaCrawler 主项目相同的许可证。仅供学习和研究使用，请遵守相关法律法规和平台使用条款。

## 📞 支持

- **文档**: 查看应用内的帮助文档
- **Issues**: 在 GitHub 上提交问题
- **讨论**: 参与社区讨论

---

**享受使用 MediaCrawler Streamlit Web 测试工具！** 🎉
