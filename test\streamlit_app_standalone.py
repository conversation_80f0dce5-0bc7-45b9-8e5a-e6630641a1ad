#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MediaCrawler Streamlit Web 测试工具 (独立版本)
不依赖原始爬虫模块的独立版本
"""

import streamlit as st
import sys
import os
import subprocess
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# 页面配置
st.set_page_config(
    page_title="MediaCrawler 测试工具",
    page_icon="🕷️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义 CSS 样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .status-card {
        background: linear-gradient(90deg, #f0f2f6 0%, #ffffff 100%);
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #1f77b4;
        margin: 1rem 0;
    }
    
    .success-card {
        border-left-color: #28a745;
        background: linear-gradient(90deg, #d4edda 0%, #ffffff 100%);
    }
    
    .warning-card {
        border-left-color: #ffc107;
        background: linear-gradient(90deg, #fff3cd 0%, #ffffff 100%);
    }
    
    .error-card {
        border-left-color: #dc3545;
        background: linear-gradient(90deg, #f8d7da 0%, #ffffff 100%);
    }
    
    .platform-status {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem;
        margin: 0.25rem 0;
        border-radius: 5px;
        background: #f8f9fa;
    }
</style>
""", unsafe_allow_html=True)

class StandaloneTester:
    """独立测试器"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.platforms = {
            "xhs": {"name": "小红书", "icon": "📱", "keywords": ["美食", "旅行", "穿搭"]},
            "dy": {"name": "抖音", "icon": "🎵", "keywords": ["搞笑", "音乐", "舞蹈"]},
            "ks": {"name": "快手", "icon": "⚡", "keywords": ["美食", "生活", "搞笑"]},
            "bili": {"name": "B站", "icon": "📺", "keywords": ["动画", "游戏", "科技"]},
            "wb": {"name": "微博", "icon": "🐦", "keywords": ["热搜", "新闻", "娱乐"]},
            "tieba": {"name": "贴吧", "icon": "💬", "keywords": ["游戏", "动漫", "科技"]},
            "zhihu": {"name": "知乎", "icon": "🧠", "keywords": ["编程", "科技", "职场"]}
        }
    
    def check_system_status(self) -> Dict[str, Any]:
        """检查系统状态"""
        status = {
            "python_ok": False,
            "python_version": "",
            "dependencies_ok": False,
            "config_ok": False,
            "main_script_ok": False
        }
        
        try:
            # 检查 Python 版本
            version = sys.version_info
            status["python_version"] = f"{version.major}.{version.minor}.{version.micro}"
            status["python_ok"] = version >= (3, 9)
            
            # 检查主脚本
            main_script = os.path.join(self.project_root, "main.py")
            status["main_script_ok"] = os.path.exists(main_script)
            
            # 检查配置文件
            config_files = ["config/base_config.py", "config/db_config.py"]
            config_exists = all(
                os.path.exists(os.path.join(self.project_root, config_file))
                for config_file in config_files
            )
            status["config_ok"] = config_exists
            
            # 简单检查依赖
            try:
                import playwright
                status["dependencies_ok"] = True
            except ImportError:
                status["dependencies_ok"] = False
                
        except Exception as e:
            print(f"系统状态检查失败: {str(e)}")
        
        return status
    
    def test_platform_command(self, platform_key: str, timeout: int = 10) -> Dict:
        """测试平台命令"""
        result = {
            "platform": platform_key,
            "name": self.platforms[platform_key]["name"],
            "status": "未测试",
            "command_available": False,
            "help_output": "",
            "error": None,
            "duration": 0
        }
        
        start_time = time.time()
        
        try:
            # 测试 help 命令
            cmd = [sys.executable, "main.py", "--platform", platform_key, "--help"]
            
            process = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            if process.returncode == 0:
                result["command_available"] = True
                result["help_output"] = process.stdout[:500]  # 限制输出长度
                result["status"] = "可用"
            else:
                result["status"] = "命令错误"
                result["error"] = process.stderr[:200]
                
        except subprocess.TimeoutExpired:
            result["status"] = "超时"
            result["error"] = f"命令执行超过 {timeout} 秒"
        except Exception as e:
            result["status"] = "异常"
            result["error"] = str(e)
        
        result["duration"] = time.time() - start_time
        return result
    
    def run_quick_test(self, selected_platforms: List[str], progress_callback=None) -> Dict:
        """运行快速测试"""
        results = {
            "test_type": "quick_test",
            "start_time": datetime.now(),
            "platforms": {},
            "summary": {"total": len(selected_platforms), "passed": 0, "failed": 0}
        }
        
        for i, platform_key in enumerate(selected_platforms):
            if progress_callback:
                progress_callback(f"测试 {self.platforms[platform_key]['name']} ({i+1}/{len(selected_platforms)})")
            
            result = self.test_platform_command(platform_key)
            results["platforms"][platform_key] = result
            
            if result["status"] == "可用":
                results["summary"]["passed"] += 1
            else:
                results["summary"]["failed"] += 1
        
        results["end_time"] = datetime.now()
        return results

def init_session_state():
    """初始化 session state"""
    if 'tester' not in st.session_state:
        st.session_state.tester = StandaloneTester()
    
    if 'test_results' not in st.session_state:
        st.session_state.test_results = None

def render_header():
    """渲染页面头部"""
    st.markdown('<h1 class="main-header">🕷️ MediaCrawler 测试工具</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    <div style="text-align: center; color: #666; margin-bottom: 2rem;">
        MediaCrawler Web 测试工具 - 独立版本
    </div>
    """, unsafe_allow_html=True)

def render_system_overview():
    """渲染系统概览"""
    st.subheader("📊 系统状态")
    
    if st.button("🔄 刷新状态"):
        st.session_state.system_status = None
    
    if 'system_status' not in st.session_state or st.session_state.system_status is None:
        with st.spinner("正在检查系统状态..."):
            st.session_state.system_status = st.session_state.tester.check_system_status()
    
    system_status = st.session_state.system_status
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        python_status = "✅" if system_status.get("python_ok", False) else "❌"
        st.metric("Python 环境", python_status, system_status.get("python_version", "未知"))
    
    with col2:
        deps_status = "✅" if system_status.get("dependencies_ok", False) else "❌"
        st.metric("依赖包", deps_status, "Playwright 等")
    
    with col3:
        config_status = "✅" if system_status.get("config_ok", False) else "❌"
        st.metric("配置文件", config_status, "完整" if system_status.get("config_ok", False) else "缺失")
    
    with col4:
        main_status = "✅" if system_status.get("main_script_ok", False) else "❌"
        st.metric("主脚本", main_status, "main.py")

def render_platform_test():
    """渲染平台测试"""
    st.subheader("🔧 平台测试")
    
    platforms = st.session_state.tester.platforms
    
    # 平台选择
    st.markdown("**选择要测试的平台：**")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("全选"):
            for platform_key in platforms.keys():
                st.session_state[f"platform_{platform_key}"] = True
    
    with col2:
        if st.button("取消全选"):
            for platform_key in platforms.keys():
                st.session_state[f"platform_{platform_key}"] = False
    
    # 平台复选框
    selected_platforms = []
    
    cols = st.columns(2)
    platform_items = list(platforms.items())
    
    for i, (platform_key, platform_info) in enumerate(platform_items):
        col = cols[i % 2]
        with col:
            key = f"platform_{platform_key}"
            if key not in st.session_state:
                st.session_state[key] = True
            
            if st.checkbox(
                f"{platform_info['icon']} {platform_info['name']} ({platform_key})",
                value=st.session_state[key],
                key=key
            ):
                selected_platforms.append(platform_key)
    
    # 测试执行
    if selected_platforms:
        if st.button("开始测试", type="primary"):
            with st.spinner("正在执行测试..."):
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                def progress_callback(message):
                    status_text.text(message)
                
                results = st.session_state.tester.run_quick_test(selected_platforms, progress_callback)
                st.session_state.test_results = results
                
                progress_bar.progress(1.0)
                status_text.text("测试完成！")
                st.rerun()
    else:
        st.warning("请至少选择一个平台进行测试")

def render_test_results():
    """渲染测试结果"""
    if not st.session_state.test_results:
        return
    
    st.subheader("📊 测试结果")
    
    results = st.session_state.test_results
    summary = results.get("summary", {})
    
    # 摘要
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("总平台数", summary.get("total", 0))
    
    with col2:
        st.metric("通过", summary.get("passed", 0))
    
    with col3:
        st.metric("失败", summary.get("failed", 0))
    
    # 详细结果
    st.markdown("### 详细结果")
    
    for platform_key, result in results.get("platforms", {}).items():
        with st.expander(f"{result['name']} - {result['status']}"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"**状态**: {result['status']}")
                st.write(f"**耗时**: {result['duration']:.2f}秒")
                st.write(f"**命令可用**: {'是' if result['command_available'] else '否'}")
            
            with col2:
                if result.get('error'):
                    st.error(f"错误: {result['error']}")
                
                if result.get('help_output'):
                    st.code(result['help_output'][:200] + "..." if len(result['help_output']) > 200 else result['help_output'])

def render_manual_test():
    """渲染手动测试"""
    st.subheader("🚀 手动测试")
    
    st.markdown("""
    您也可以手动运行 MediaCrawler 命令进行测试：
    """)
    
    platforms = st.session_state.tester.platforms
    
    # 平台选择
    platform_key = st.selectbox(
        "选择平台",
        options=list(platforms.keys()),
        format_func=lambda x: f"{platforms[x]['icon']} {platforms[x]['name']} ({x})"
    )
    
    # 参数配置
    col1, col2 = st.columns(2)
    
    with col1:
        login_type = st.selectbox("登录方式", ["qrcode", "phone", "cookie"])
        crawl_type = st.selectbox("爬取类型", ["search", "detail", "creator"])
    
    with col2:
        keywords = st.text_input("关键词", value="测试")
        save_format = st.selectbox("保存格式", ["json", "csv", "db"])
    
    # 生成命令
    cmd_parts = [
        "python main.py",
        f"--platform {platform_key}",
        f"--lt {login_type}",
        f"--type {crawl_type}",
        f"--keywords {keywords}",
        f"--save_data_option {save_format}"
    ]
    
    command = " ".join(cmd_parts)
    
    st.markdown("**生成的命令：**")
    st.code(command)
    
    st.markdown("**执行步骤：**")
    st.markdown("""
    1. 打开命令行终端
    2. 切换到 MediaCrawler 项目目录
    3. 复制上述命令并执行
    4. 根据提示完成登录和爬取
    """)

def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.title("🕷️ MediaCrawler")
        st.markdown("---")
        
        # 系统状态
        if 'system_status' in st.session_state:
            system_status = st.session_state.system_status
            overall_ok = all([
                system_status.get("python_ok", False),
                system_status.get("dependencies_ok", False),
                system_status.get("config_ok", False),
                system_status.get("main_script_ok", False)
            ])
            
            status_text = "🟢 系统正常" if overall_ok else "🟡 需要检查"
            st.markdown(f"**系统状态**: {status_text}")
        
        st.markdown("---")
        
        # 功能说明
        st.markdown("### 📋 功能说明")
        st.markdown("""
        - **系统状态**: 检查环境配置
        - **平台测试**: 批量测试平台可用性
        - **手动测试**: 生成测试命令
        """)
        
        st.markdown("---")
        st.markdown("### ℹ️ 关于")
        st.markdown("""
        **版本**: v1.0.0 (独立版)  
        **更新**: 2025-07-09  
        **说明**: 不依赖原始模块的独立版本
        """)

def main():
    """主函数"""
    init_session_state()
    
    render_sidebar()
    render_header()
    
    # 创建标签页
    tab1, tab2, tab3 = st.tabs(["📊 系统状态", "🔧 平台测试", "🚀 手动测试"])
    
    with tab1:
        render_system_overview()
    
    with tab2:
        render_platform_test()
        render_test_results()
    
    with tab3:
        render_manual_test()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.8rem;">
        MediaCrawler Web 测试工具 (独立版) | 仅供学习和研究使用
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
