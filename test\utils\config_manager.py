#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责管理测试配置和系统设置
"""

import os
import json
from typing import Dict, Any, List
from datetime import datetime


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self):
        self.config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config")
        self.config_file = os.path.join(self.config_dir, "streamlit_config.json")
        self.default_config = self._get_default_config()
        self._ensure_config_dir()
    
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        os.makedirs(self.config_dir, exist_ok=True)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "test_settings": {
                "default_timeout": 30,
                "default_keywords": "测试",
                "default_save_format": "json",
                "default_login_type": "qrcode",
                "enable_comments": False,
                "max_concurrent_tests": 3
            },
            "ui_settings": {
                "theme": "light",
                "auto_refresh": True,
                "show_debug_info": False,
                "items_per_page": 10
            },
            "platform_settings": {
                "xhs": {
                    "enabled": True,
                    "default_keywords": ["美食", "旅行", "穿搭"],
                    "timeout": 30
                },
                "dy": {
                    "enabled": True,
                    "default_keywords": ["搞笑", "音乐", "舞蹈"],
                    "timeout": 30
                },
                "ks": {
                    "enabled": True,
                    "default_keywords": ["美食", "生活", "搞笑"],
                    "timeout": 30
                },
                "bili": {
                    "enabled": True,
                    "default_keywords": ["动画", "游戏", "科技"],
                    "timeout": 30
                },
                "wb": {
                    "enabled": True,
                    "default_keywords": ["热搜", "新闻", "娱乐"],
                    "timeout": 30
                },
                "tieba": {
                    "enabled": True,
                    "default_keywords": ["游戏", "动漫", "科技"],
                    "timeout": 30
                },
                "zhihu": {
                    "enabled": True,
                    "default_keywords": ["编程", "科技", "职场"],
                    "timeout": 30
                }
            },
            "last_updated": datetime.now().isoformat()
        }
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保新增的配置项存在
                return self._merge_config(self.default_config, config)
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置失败: {str(e)}")
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置"""
        try:
            config["last_updated"] = datetime.now().isoformat()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
            return False
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """合并配置，确保默认配置项存在"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get_test_settings(self) -> Dict[str, Any]:
        """获取测试设置"""
        config = self.load_config()
        return config.get("test_settings", {})
    
    def update_test_settings(self, settings: Dict[str, Any]) -> bool:
        """更新测试设置"""
        config = self.load_config()
        config["test_settings"].update(settings)
        return self.save_config(config)
    
    def get_platform_settings(self, platform_key: str = None) -> Dict[str, Any]:
        """获取平台设置"""
        config = self.load_config()
        platform_settings = config.get("platform_settings", {})
        
        if platform_key:
            return platform_settings.get(platform_key, {})
        return platform_settings
    
    def update_platform_settings(self, platform_key: str, settings: Dict[str, Any]) -> bool:
        """更新平台设置"""
        config = self.load_config()
        if "platform_settings" not in config:
            config["platform_settings"] = {}
        
        if platform_key not in config["platform_settings"]:
            config["platform_settings"][platform_key] = {}
        
        config["platform_settings"][platform_key].update(settings)
        return self.save_config(config)
    
    def get_ui_settings(self) -> Dict[str, Any]:
        """获取 UI 设置"""
        config = self.load_config()
        return config.get("ui_settings", {})
    
    def update_ui_settings(self, settings: Dict[str, Any]) -> bool:
        """更新 UI 设置"""
        config = self.load_config()
        config["ui_settings"].update(settings)
        return self.save_config(config)
    
    def get_enabled_platforms(self) -> List[str]:
        """获取启用的平台列表"""
        platform_settings = self.get_platform_settings()
        return [
            platform_key for platform_key, settings in platform_settings.items()
            if settings.get("enabled", True)
        ]
    
    def reset_config(self) -> bool:
        """重置配置为默认值"""
        return self.save_config(self.default_config.copy())
    
    def export_config(self) -> str:
        """导出配置为 JSON 字符串"""
        config = self.load_config()
        return json.dumps(config, ensure_ascii=False, indent=2)
    
    def import_config(self, config_json: str) -> bool:
        """从 JSON 字符串导入配置"""
        try:
            config = json.loads(config_json)
            return self.save_config(config)
        except Exception as e:
            print(f"导入配置失败: {str(e)}")
            return False
    
    def get_platform_keywords(self, platform_key: str) -> List[str]:
        """获取平台的默认关键词"""
        platform_settings = self.get_platform_settings(platform_key)
        return platform_settings.get("default_keywords", ["测试"])
    
    def get_platform_timeout(self, platform_key: str) -> int:
        """获取平台的超时时间"""
        platform_settings = self.get_platform_settings(platform_key)
        return platform_settings.get("timeout", 30)
    
    def is_platform_enabled(self, platform_key: str) -> bool:
        """检查平台是否启用"""
        platform_settings = self.get_platform_settings(platform_key)
        return platform_settings.get("enabled", True)
