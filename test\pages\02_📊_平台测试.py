#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台测试页面
提供单个平台的详细测试功能
"""

import streamlit as st
import sys
import os
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from test.utils.test_runner import TestRunner
from test.utils.config_manager import ConfigManager

# 页面配置
st.set_page_config(
    page_title="平台测试 - MediaCrawler",
    page_icon="📊",
    layout="wide"
)

def init_session_state():
    """初始化 session state"""
    if 'test_runner' not in st.session_state:
        st.session_state.test_runner = TestRunner()
    
    if 'config_manager' not in st.session_state:
        st.session_state.config_manager = ConfigManager()

def render_header():
    """渲染页面头部"""
    st.title("📊 平台测试")
    st.markdown("---")
    
    st.markdown("""
    平台测试功能提供对单个平台的详细测试，包括：
    - 深度功能检查
    - 配置验证
    - 性能测试
    - 错误诊断
    """)

def render_platform_selector():
    """渲染平台选择器"""
    st.subheader("🎯 选择测试平台")
    
    platforms = st.session_state.test_runner.platforms
    
    # 创建平台选择
    platform_options = {
        f"{info['icon']} {info['name']} ({key})": key 
        for key, info in platforms.items()
    }
    
    selected_display = st.selectbox(
        "选择要测试的平台：",
        options=list(platform_options.keys()),
        index=0
    )
    
    selected_platform = platform_options[selected_display]
    platform_info = platforms[selected_platform]
    
    # 显示平台信息
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.info(f"**平台名称**: {platform_info['name']}")
    
    with col2:
        st.info(f"**平台代码**: {selected_platform}")
    
    with col3:
        keywords = ", ".join(platform_info['test_keywords'][:2])
        st.info(f"**测试关键词**: {keywords}")
    
    return selected_platform, platform_info

def render_test_options():
    """渲染测试选项"""
    st.subheader("⚙️ 测试选项")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**基础测试**")
        test_import = st.checkbox("模块导入测试", value=True)
        test_instance = st.checkbox("实例创建测试", value=True)
        test_methods = st.checkbox("方法检查测试", value=True)
        
        st.markdown("**高级测试**")
        test_config = st.checkbox("配置验证测试", value=False)
        test_performance = st.checkbox("性能测试", value=False)
    
    with col2:
        st.markdown("**测试参数**")
        timeout = st.slider("超时时间 (秒)", 5, 60, 15)
        retry_count = st.slider("重试次数", 0, 5, 1)
        
        st.markdown("**输出选项**")
        verbose = st.checkbox("详细输出", value=True)
        save_log = st.checkbox("保存日志", value=True)
    
    return {
        "basic_tests": {
            "import": test_import,
            "instance": test_instance,
            "methods": test_methods
        },
        "advanced_tests": {
            "config": test_config,
            "performance": test_performance
        },
        "parameters": {
            "timeout": timeout,
            "retry_count": retry_count,
            "verbose": verbose,
            "save_log": save_log
        }
    }

def run_platform_test(platform_key, platform_info, test_options):
    """运行平台测试"""
    results = {
        "platform": platform_key,
        "name": platform_info["name"],
        "start_time": datetime.now(),
        "tests": {},
        "overall_status": "未开始",
        "errors": []
    }
    
    progress_bar = st.progress(0)
    status_text = st.empty()
    log_container = st.container()
    
    test_count = 0
    total_tests = sum([
        len([k for k, v in test_options["basic_tests"].items() if v]),
        len([k for k, v in test_options["advanced_tests"].items() if v])
    ])
    
    if total_tests == 0:
        st.warning("请至少选择一个测试项目")
        return None
    
    with log_container:
        log_area = st.empty()
        logs = []
    
    def log_message(message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        logs.append(log_entry)
        if test_options["parameters"]["verbose"]:
            log_area.text_area("测试日志", "\n".join(logs[-15:]), height=200)
    
    try:
        # 基础测试
        if test_options["basic_tests"]["import"]:
            test_count += 1
            progress_bar.progress(test_count / total_tests)
            status_text.text(f"正在进行模块导入测试... ({test_count}/{total_tests})")
            
            log_message("开始模块导入测试")
            try:
                crawler_class = platform_info["crawler"]
                results["tests"]["import"] = {
                    "status": "通过",
                    "message": "模块导入成功",
                    "details": f"成功导入 {crawler_class.__name__}"
                }
                log_message("✅ 模块导入测试通过")
            except Exception as e:
                results["tests"]["import"] = {
                    "status": "失败",
                    "message": f"模块导入失败: {str(e)}",
                    "details": str(e)
                }
                results["errors"].append(f"导入错误: {str(e)}")
                log_message(f"❌ 模块导入测试失败: {str(e)}")
        
        if test_options["basic_tests"]["instance"]:
            test_count += 1
            progress_bar.progress(test_count / total_tests)
            status_text.text(f"正在进行实例创建测试... ({test_count}/{total_tests})")
            
            log_message("开始实例创建测试")
            try:
                crawler = platform_info["crawler"]()
                results["tests"]["instance"] = {
                    "status": "通过",
                    "message": "实例创建成功",
                    "details": f"成功创建 {type(crawler).__name__} 实例"
                }
                log_message("✅ 实例创建测试通过")
                
                # 保存实例用于后续测试
                test_instance = crawler
                
            except Exception as e:
                results["tests"]["instance"] = {
                    "status": "失败",
                    "message": f"实例创建失败: {str(e)}",
                    "details": str(e)
                }
                results["errors"].append(f"实例创建错误: {str(e)}")
                log_message(f"❌ 实例创建测试失败: {str(e)}")
                test_instance = None
        
        if test_options["basic_tests"]["methods"] and 'test_instance' in locals():
            test_count += 1
            progress_bar.progress(test_count / total_tests)
            status_text.text(f"正在进行方法检查测试... ({test_count}/{total_tests})")
            
            log_message("开始方法检查测试")
            
            required_methods = ["start", "search", "launch_browser"]
            method_results = {}
            
            for method in required_methods:
                has_method = hasattr(test_instance, method)
                method_results[method] = has_method
                log_message(f"检查方法 {method}: {'✅' if has_method else '❌'}")
            
            all_methods_exist = all(method_results.values())
            
            results["tests"]["methods"] = {
                "status": "通过" if all_methods_exist else "部分通过",
                "message": f"方法检查完成，{sum(method_results.values())}/{len(required_methods)} 个方法存在",
                "details": method_results
            }
            
            if all_methods_exist:
                log_message("✅ 方法检查测试通过")
            else:
                missing = [m for m, exists in method_results.items() if not exists]
                log_message(f"⚠️ 方法检查部分通过，缺少方法: {', '.join(missing)}")
        
        # 高级测试
        if test_options["advanced_tests"]["config"]:
            test_count += 1
            progress_bar.progress(test_count / total_tests)
            status_text.text(f"正在进行配置验证测试... ({test_count}/{total_tests})")
            
            log_message("开始配置验证测试")
            
            # 检查平台配置
            platform_config = st.session_state.config_manager.get_platform_settings(platform_key)
            
            results["tests"]["config"] = {
                "status": "通过",
                "message": "配置验证完成",
                "details": {
                    "enabled": platform_config.get("enabled", True),
                    "keywords_count": len(platform_config.get("default_keywords", [])),
                    "timeout": platform_config.get("timeout", 30)
                }
            }
            log_message("✅ 配置验证测试通过")
        
        if test_options["advanced_tests"]["performance"]:
            test_count += 1
            progress_bar.progress(test_count / total_tests)
            status_text.text(f"正在进行性能测试... ({test_count}/{total_tests})")
            
            log_message("开始性能测试")
            
            import time
            start_time = time.time()
            
            # 模拟性能测试
            for i in range(3):
                try:
                    crawler = platform_info["crawler"]()
                    time.sleep(0.1)  # 模拟操作
                except:
                    pass
            
            end_time = time.time()
            duration = end_time - start_time
            
            results["tests"]["performance"] = {
                "status": "通过" if duration < 2.0 else "警告",
                "message": f"性能测试完成，耗时 {duration:.2f} 秒",
                "details": {
                    "duration": duration,
                    "iterations": 3,
                    "avg_time": duration / 3
                }
            }
            
            if duration < 2.0:
                log_message(f"✅ 性能测试通过，耗时 {duration:.2f} 秒")
            else:
                log_message(f"⚠️ 性能测试警告，耗时较长: {duration:.2f} 秒")
    
    except Exception as e:
        results["errors"].append(f"测试执行错误: {str(e)}")
        log_message(f"❌ 测试执行异常: {str(e)}")
    
    finally:
        results["end_time"] = datetime.now()
        results["duration"] = (results["end_time"] - results["start_time"]).total_seconds()
        
        # 计算总体状态
        test_statuses = [test["status"] for test in results["tests"].values()]
        if all(status == "通过" for status in test_statuses):
            results["overall_status"] = "全部通过"
        elif any(status == "失败" for status in test_statuses):
            results["overall_status"] = "部分失败"
        else:
            results["overall_status"] = "部分通过"
        
        progress_bar.progress(1.0)
        status_text.text("测试完成！")
        log_message(f"测试完成，总体状态: {results['overall_status']}")
    
    return results

def render_test_results(results):
    """渲染测试结果"""
    if not results:
        return
    
    st.subheader("📋 测试结果")
    
    # 总体状态
    status_color = {
        "全部通过": "success",
        "部分通过": "warning", 
        "部分失败": "error"
    }.get(results["overall_status"], "info")
    
    getattr(st, status_color)(f"总体状态: {results['overall_status']}")
    
    # 测试摘要
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("测试项目", len(results["tests"]))
    
    with col2:
        passed = len([t for t in results["tests"].values() if t["status"] == "通过"])
        st.metric("通过项目", passed)
    
    with col3:
        st.metric("测试耗时", f"{results['duration']:.2f}s")
    
    # 详细结果
    st.markdown("### 详细结果")
    
    for test_name, test_result in results["tests"].items():
        with st.expander(f"{test_name.upper()} - {test_result['status']}"):
            st.write(f"**状态**: {test_result['status']}")
            st.write(f"**消息**: {test_result['message']}")
            
            if test_result.get("details"):
                st.write("**详细信息**:")
                if isinstance(test_result["details"], dict):
                    for key, value in test_result["details"].items():
                        st.write(f"- {key}: {value}")
                else:
                    st.write(test_result["details"])
    
    # 错误信息
    if results["errors"]:
        st.markdown("### ❌ 错误信息")
        for error in results["errors"]:
            st.error(error)

def main():
    """主函数"""
    init_session_state()
    
    render_header()
    
    # 平台选择
    selected_platform, platform_info = render_platform_selector()
    
    # 测试选项
    test_options = render_test_options()
    
    # 测试执行
    st.subheader("🚀 执行测试")
    
    if st.button("开始测试", type="primary", use_container_width=True):
        with st.spinner("正在执行测试..."):
            results = run_platform_test(selected_platform, platform_info, test_options)
            if results:
                st.session_state.platform_test_results = results
    
    # 显示结果
    if hasattr(st.session_state, 'platform_test_results'):
        render_test_results(st.session_state.platform_test_results)

if __name__ == "__main__":
    main()
