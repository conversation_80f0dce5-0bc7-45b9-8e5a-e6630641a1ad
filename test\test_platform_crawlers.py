#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MediaCrawler 平台爬蟲測試程式
測試所有支援的7個平台的基本爬取功能
"""

import asyncio
import sys
import os
import time
from typing import Dict, List, Optional
from datetime import datetime

# 添加專案根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
from media_platform.xhs import XiaoHongShuCrawler
from media_platform.douyin import DouYinCrawler
from media_platform.kuaishou import KuaishouCrawler
from media_platform.bilibili import BilibiliCrawler
from media_platform.weibo import WeiboCrawler
from media_platform.tieba import TieBaCrawler
from media_platform.zhihu import ZhihuCrawler


class PlatformTester:
    """平台爬蟲測試器"""
    
    def __init__(self):
        self.test_results = {}
        self.test_keywords = ["測試", "科技", "編程"]
        
        # 平台配置
        self.platforms = {
            "xhs": {
                "name": "小紅書",
                "crawler": <PERSON><PERSON><PERSON>ShuCrawler,
                "test_keywords": ["美食", "旅行", "穿搭"]
            },
            "dy": {
                "name": "抖音",
                "crawler": DouYinCrawler,
                "test_keywords": ["搞笑", "音樂", "舞蹈"]
            },
            "ks": {
                "name": "快手",
                "crawler": KuaishouCrawler,
                "test_keywords": ["美食", "生活", "搞笑"]
            },
            "bili": {
                "name": "B站",
                "crawler": BilibiliCrawler,
                "test_keywords": ["動畫", "遊戲", "科技"]
            },
            "wb": {
                "name": "微博",
                "crawler": WeiboCrawler,
                "test_keywords": ["熱搜", "新聞", "娛樂"]
            },
            "tieba": {
                "name": "貼吧",
                "crawler": TieBaCrawler,
                "test_keywords": ["遊戲", "動漫", "科技"]
            },
            "zhihu": {
                "name": "知乎",
                "crawler": ZhihuCrawler,
                "test_keywords": ["程式設計", "科技", "職場"]
            }
        }
    
    def print_header(self, title: str):
        """列印標題"""
        print("\n" + "="*60)
        print(f"  {title}")
        print("="*60)
    
    def print_platform_info(self, platform_key: str):
        """列印平台資訊"""
        platform = self.platforms[platform_key]
        print(f"\n🔍 測試平台: {platform['name']} ({platform_key})")
        print(f"📝 測試關鍵詞: {', '.join(platform['test_keywords'])}")
        print("-" * 40)
    
    async def test_platform_basic(self, platform_key: str) -> Dict:
        """測試單個平台的基本功能"""
        platform = self.platforms[platform_key]
        result = {
            "platform": platform_key,
            "name": platform["name"],
            "status": "未測試",
            "error": None,
            "start_time": None,
            "end_time": None,
            "duration": 0
        }

        try:
            self.print_platform_info(platform_key)
            result["start_time"] = datetime.now()

            # 設置測試配置
            print("🔧 正在設置測試配置...")
            original_config = self.backup_config()
            self.set_test_config(platform_key, platform["test_keywords"][0])

            # 創建爬蟲實例
            print("📦 正在創建爬蟲實例...")
            crawler = platform["crawler"]()

            # 測試基本屬性
            print("🔍 正在檢查爬蟲屬性...")
            if hasattr(crawler, 'start'):
                print("   ✅ 具有 start 方法")
            if hasattr(crawler, 'search'):
                print("   ✅ 具有 search 方法")
            if hasattr(crawler, 'launch_browser'):
                print("   ✅ 具有 launch_browser 方法")

            print("✅ 爬蟲基本檢查通過")
            result["status"] = "基本檢查通過"

            # 恢復原始配置
            self.restore_config(original_config)

        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
            result["status"] = "失敗"
            result["error"] = str(e)

        finally:
            result["end_time"] = datetime.now()
            if result["start_time"]:
                result["duration"] = (result["end_time"] - result["start_time"]).total_seconds()

        return result

    def backup_config(self) -> Dict:
        """備份當前配置"""
        return {
            "PLATFORM": getattr(config, "PLATFORM", "xhs"),
            "KEYWORDS": getattr(config, "KEYWORDS", "測試"),
            "LOGIN_TYPE": getattr(config, "LOGIN_TYPE", "qrcode"),
            "CRAWLER_TYPE": getattr(config, "CRAWLER_TYPE", "search")
        }

    def set_test_config(self, platform_key: str, keyword: str):
        """設置測試配置"""
        config.PLATFORM = platform_key
        config.KEYWORDS = keyword
        config.LOGIN_TYPE = "qrcode"
        config.CRAWLER_TYPE = "search"
        config.SAVE_DATA_OPTION = "json"
        config.ENABLE_GET_COMMENTS = False

    def restore_config(self, original_config: Dict):
        """恢復原始配置"""
        for key, value in original_config.items():
            setattr(config, key, value)
    
    async def test_all_platforms(self) -> Dict:
        """測試所有平台"""
        self.print_header("MediaCrawler 平台爬蟲測試")
        print("🚀 開始測試所有支援的平台...")
        
        all_results = {}
        
        for platform_key in self.platforms.keys():
            try:
                result = await self.test_platform_basic(platform_key)
                all_results[platform_key] = result
                
                # 短暫延遲避免資源衝突
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"❌ 平台 {platform_key} 測試過程中發生錯誤: {str(e)}")
                all_results[platform_key] = {
                    "platform": platform_key,
                    "name": self.platforms[platform_key]["name"],
                    "status": "測試異常",
                    "error": str(e),
                    "duration": 0
                }
        
        return all_results
    
    def print_test_summary(self, results: Dict):
        """列印測試總結"""
        self.print_header("測試結果總結")
        
        success_count = 0
        total_count = len(results)
        
        print(f"📊 測試統計:")
        print(f"   總平台數: {total_count}")

        for platform_key, result in results.items():
            status_icon = "✅" if result["status"] == "基本檢查通過" else "❌"
            duration = f"{result['duration']:.2f}s" if result['duration'] > 0 else "N/A"

            print(f"   {status_icon} {result['name']} ({platform_key}): {result['status']} ({duration})")

            if result["status"] == "基本檢查通過":
                success_count += 1
            elif result.get("error"):
                print(f"      錯誤詳情: {result['error']}")

        print(f"\n🎯 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print("\n🎉 所有平台測試通過！可以開始正式爬取數據。")
        else:
            print(f"\n⚠️  有 {total_count - success_count} 個平台測試失敗，請檢查配置或網路連接。")
    
    def save_test_report(self, results: Dict):
        """保存測試報告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"test/platform_test_report_{timestamp}.txt"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("MediaCrawler 平台測試報告\n")
                f.write("=" * 50 + "\n")
                f.write(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                for platform_key, result in results.items():
                    f.write(f"平台: {result['name']} ({platform_key})\n")
                    f.write(f"狀態: {result['status']}\n")
                    f.write(f"耗時: {result['duration']:.2f}秒\n")
                    if result.get('error'):
                        f.write(f"錯誤: {result['error']}\n")
                    f.write("-" * 30 + "\n")
            
            print(f"\n📄 測試報告已保存至: {report_file}")
            
        except Exception as e:
            print(f"⚠️  保存測試報告失敗: {str(e)}")


async def main():
    """主函數"""
    print("🔧 MediaCrawler 平台爬蟲測試工具")
    print("📋 本工具將測試所有7個支援平台的基本功能")
    
    # 創建測試器
    tester = PlatformTester()
    
    # 執行測試
    results = await tester.test_all_platforms()
    
    # 列印結果
    tester.print_test_summary(results)
    
    # 保存報告
    tester.save_test_report(results)
    
    print("\n🏁 測試完成！")


if __name__ == "__main__":
    # 設置事件循環策略 (Windows 相容性)
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 運行測試
    asyncio.run(main())
