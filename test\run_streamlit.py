#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit Web 应用启动脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import streamlit
        import plotly
        import pandas
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install streamlit plotly pandas")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动 MediaCrawler Streamlit Web 应用")
    parser.add_argument("--port", type=int, default=8501, help="端口号 (默认: 8501)")
    parser.add_argument("--host", type=str, default="localhost", help="主机地址 (默认: localhost)")
    parser.add_argument("--dev", action="store_true", help="开发模式 (启用调试)")
    parser.add_argument("--standalone", action="store_true", help="使用独立版本 (不依赖原始模块)")

    args = parser.parse_args()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 获取脚本目录
    script_dir = Path(__file__).parent

    # 选择应用文件
    if args.standalone:
        app_file = script_dir / "streamlit_app_standalone.py"
        print("🔧 使用独立版本 (不依赖原始爬虫模块)")
    else:
        app_file = script_dir / "streamlit_app.py"
        print("🔧 使用完整版本 (需要原始爬虫模块)")

    if not app_file.exists():
        print(f"❌ 找不到应用文件: {app_file}")
        if not args.standalone:
            print("💡 提示: 如果遇到模块导入问题，可以尝试使用 --standalone 参数")
        sys.exit(1)
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        str(app_file),
        "--server.port", str(args.port),
        "--server.address", args.host,
        "--server.headless", "true",
        "--browser.gatherUsageStats", "false"
    ]
    
    if args.dev:
        cmd.extend([
            "--server.runOnSave", "true",
            "--server.allowRunOnSave", "true"
        ])
    
    print(f"🚀 启动 MediaCrawler Web 测试工具...")
    print(f"📍 地址: http://{args.host}:{args.port}")
    print(f"📁 工作目录: {script_dir}")
    print(f"🔧 命令: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        # 切换到应用目录
        os.chdir(script_dir)
        
        # 启动应用
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
