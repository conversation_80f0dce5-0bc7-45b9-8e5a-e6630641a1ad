#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MediaCrawler 真實爬取測試程式
使用實際的 main.py 邏輯來測試各平台
"""

import asyncio
import sys
import os
import subprocess
import time
from typing import Dict, List, Optional
from datetime import datetime

# 添加專案根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class RealCrawlTester:
    """真實爬取測試器"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.test_results = {}
        
        # 測試配置 - 每個平台的測試參數
        self.test_configs = {
            "xhs": {
                "name": "小紅書",
                "keywords": "美食",
                "timeout": 30,
                "description": "測試小紅書搜索功能"
            },
            "dy": {
                "name": "抖音",
                "keywords": "搞笑",
                "timeout": 30,
                "description": "測試抖音搜索功能"
            },
            "ks": {
                "name": "快手",
                "keywords": "美食",
                "timeout": 30,
                "description": "測試快手搜索功能"
            },
            "bili": {
                "name": "B站",
                "keywords": "科技",
                "timeout": 30,
                "description": "測試B站搜索功能"
            },
            "wb": {
                "name": "微博",
                "keywords": "科技",
                "timeout": 30,
                "description": "測試微博搜索功能"
            },
            "tieba": {
                "name": "貼吧",
                "keywords": "程式設計",
                "timeout": 30,
                "description": "測試貼吧搜索功能"
            },
            "zhihu": {
                "name": "知乎",
                "keywords": "程式設計",
                "timeout": 30,
                "description": "測試知乎搜索功能"
            }
        }
    
    def print_header(self, title: str):
        """列印標題"""
        print("\n" + "="*70)
        print(f"  {title}")
        print("="*70)
    
    def print_platform_info(self, platform_key: str):
        """列印平台測試資訊"""
        config = self.test_configs[platform_key]
        print(f"\n🎯 測試平台: {config['name']} ({platform_key})")
        print(f"🔍 搜索關鍵詞: {config['keywords']}")
        print(f"⏱️  超時時間: {config['timeout']}秒")
        print(f"📝 測試描述: {config['description']}")
        print("-" * 50)
    
    async def test_platform_startup(self, platform_key: str, interactive: bool = True) -> Dict:
        """測試單個平台的啟動功能"""
        config = self.test_configs[platform_key]
        result = {
            "platform": platform_key,
            "name": config["name"],
            "status": "未開始",
            "startup_success": False,
            "login_prompt_shown": False,
            "error": None,
            "start_time": None,
            "end_time": None,
            "duration": 0,
            "output_lines": []
        }
        
        try:
            self.print_platform_info(platform_key)
            result["start_time"] = datetime.now()
            
            if interactive:
                user_input = input(f"是否要測試 {config['name']} 平台？(y/n/s=跳過): ").lower().strip()
                if user_input in ['n', 'no', 's', 'skip']:
                    result["status"] = "用戶跳過"
                    return result
            
            print("🚀 正在啟動爬蟲...")
            
            # 構建命令
            cmd = [
                sys.executable, "main.py",
                "--platform", platform_key,
                "--lt", "qrcode",
                "--type", "search",
                "--keywords", config["keywords"],
                "--save_data_option", "json",
                "--get_comment", "false"
            ]
            
            print(f"📝 執行命令: {' '.join(cmd)}")
            
            # 啟動進程
            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            print("⏳ 正在檢查啟動狀態...")
            
            # 監控輸出
            output_lines = []
            start_time = time.time()
            
            while True:
                # 檢查超時
                if time.time() - start_time > config["timeout"]:
                    print("⏰ 測試超時")
                    process.terminate()
                    result["status"] = "超時"
                    break
                
                # 讀取輸出
                try:
                    line = process.stdout.readline()
                    if not line:
                        if process.poll() is not None:
                            break
                        continue
                    
                    line = line.strip()
                    if line:
                        output_lines.append(line)
                        print(f"   📄 {line}")
                        
                        # 檢查關鍵輸出
                        if "二維碼" in line or "QR" in line or "qrcode" in line:
                            result["login_prompt_shown"] = True
                            print("✅ 檢測到登錄提示")
                        
                        if "Crawler finished" in line:
                            result["startup_success"] = True
                            print("✅ 爬蟲啟動成功")
                            break
                        
                        if "error" in line.lower() or "failed" in line.lower():
                            print("❌ 檢測到錯誤")
                            result["error"] = line
                            break
                
                except Exception as e:
                    print(f"⚠️  讀取輸出時發生錯誤: {str(e)}")
                    break
            
            # 終止進程
            if process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
            
            result["output_lines"] = output_lines
            
            # 判斷測試結果
            if result["login_prompt_shown"]:
                result["status"] = "啟動成功(等待登錄)"
            elif result["startup_success"]:
                result["status"] = "完全成功"
            elif result["error"]:
                result["status"] = "啟動失敗"
            else:
                result["status"] = "狀態未知"
            
            print(f"📊 測試結果: {result['status']}")
            
        except KeyboardInterrupt:
            print("\n⏹️  用戶中斷測試")
            result["status"] = "用戶中斷"
        except Exception as e:
            print(f"❌ 測試過程中發生錯誤: {str(e)}")
            result["status"] = "測試異常"
            result["error"] = str(e)
        
        finally:
            result["end_time"] = datetime.now()
            if result["start_time"]:
                result["duration"] = (result["end_time"] - result["start_time"]).total_seconds()
        
        return result
    
    async def run_interactive_test(self):
        """運行互動式測試"""
        self.print_header("MediaCrawler 真實爬取測試 (互動模式)")
        
        print("🔧 本測試將使用實際的 main.py 來測試各平台")
        print("⚠️  注意：測試會啟動瀏覽器並顯示登錄界面")
        print("💡 每個平台測試約30秒，您可以選擇跳過")
        
        if input("\n是否繼續？(y/n): ").lower().strip() not in ['y', 'yes']:
            print("測試已取消")
            return
        
        results = {}
        
        for platform_key in self.test_configs.keys():
            try:
                result = await self.test_platform_startup(platform_key, interactive=True)
                results[platform_key] = result
                
                if result["status"] == "用戶中斷":
                    if input("\n是否繼續測試其他平台？(y/n): ").lower().strip() not in ['y', 'yes']:
                        break
                
                # 短暫延遲
                await asyncio.sleep(2)
                
            except KeyboardInterrupt:
                print("\n測試被用戶中斷")
                break
        
        self.print_test_summary(results)
        self.save_test_report(results, "interactive")
    
    async def run_batch_test(self, platforms: List[str] = None):
        """運行批次測試"""
        if platforms is None:
            platforms = list(self.test_configs.keys())
        
        self.print_header("MediaCrawler 真實爬取測試 (批次模式)")
        
        print(f"🎯 將測試以下平台: {', '.join(platforms)}")
        print("⚠️  注意：批次模式將自動跳過用戶交互")
        
        results = {}
        
        for platform_key in platforms:
            if platform_key not in self.test_configs:
                print(f"⚠️  未知平台: {platform_key}")
                continue
            
            result = await self.test_platform_startup(platform_key, interactive=False)
            results[platform_key] = result
            
            # 短暫延遲
            await asyncio.sleep(2)
        
        self.print_test_summary(results)
        self.save_test_report(results, "batch")
    
    def print_test_summary(self, results: Dict):
        """列印測試總結"""
        self.print_header("測試結果總結")
        
        total_count = len(results)
        success_count = sum(1 for r in results.values() 
                          if r["status"] in ["啟動成功(等待登錄)", "完全成功"])
        skip_count = sum(1 for r in results.values() if r["status"] == "用戶跳過")
        
        print(f"📊 測試統計:")
        print(f"   總測試數: {total_count}")
        print(f"   成功啟動: {success_count}")
        print(f"   用戶跳過: {skip_count}")
        print(f"   失敗: {total_count - success_count - skip_count}")
        
        print(f"\n📋 詳細結果:")
        for platform_key, result in results.items():
            status_icons = {
                "啟動成功(等待登錄)": "✅",
                "完全成功": "🎉",
                "用戶跳過": "⏭️",
                "用戶中斷": "⏹️",
                "啟動失敗": "❌",
                "超時": "⏰",
                "測試異常": "💥",
                "狀態未知": "❓"
            }
            
            icon = status_icons.get(result["status"], "❓")
            duration = f"{result['duration']:.1f}s" if result['duration'] > 0 else "N/A"
            
            extra_info = []
            if result.get("login_prompt_shown"):
                extra_info.append("登錄提示")
            if result.get("startup_success"):
                extra_info.append("啟動成功")
            
            extra_str = f" ({', '.join(extra_info)})" if extra_info else ""
            
            print(f"   {icon} {result['name']}: {result['status']}{extra_str} ({duration})")
            
            if result.get("error"):
                print(f"      ❌ 錯誤: {result['error']}")
        
        if success_count > 0:
            print(f"\n🎉 有 {success_count} 個平台成功啟動！")
        
        if success_count == total_count:
            print("🏆 所有平台測試完美通過！")
    
    def save_test_report(self, results: Dict, test_type: str):
        """保存測試報告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"test/real_crawl_test_report_{test_type}_{timestamp}.txt"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("MediaCrawler 真實爬取測試報告\n")
                f.write("=" * 50 + "\n")
                f.write(f"測試類型: {test_type}\n")
                f.write(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                for platform_key, result in results.items():
                    f.write(f"平台: {result['name']} ({platform_key})\n")
                    f.write(f"狀態: {result['status']}\n")
                    f.write(f"啟動成功: {result.get('startup_success', False)}\n")
                    f.write(f"登錄提示: {result.get('login_prompt_shown', False)}\n")
                    f.write(f"耗時: {result['duration']:.2f}秒\n")
                    if result.get('error'):
                        f.write(f"錯誤: {result['error']}\n")
                    if result.get('output_lines'):
                        f.write("輸出日誌:\n")
                        for line in result['output_lines'][-10:]:  # 只保存最後10行
                            f.write(f"  {line}\n")
                    f.write("-" * 30 + "\n")
            
            print(f"\n📄 測試報告已保存: {report_file}")
            
        except Exception as e:
            print(f"⚠️  保存測試報告失敗: {str(e)}")


async def main():
    """主函數"""
    print("🔧 MediaCrawler 真實爬取測試工具")
    
    tester = RealCrawlTester()
    
    print("\n請選擇測試模式:")
    print("1. 互動模式 (逐一測試，可選擇跳過)")
    print("2. 批次模式 (自動測試所有平台)")
    print("3. 自定義平台測試")
    
    choice = input("請輸入選擇 (1/2/3): ").strip()
    
    if choice == "1":
        await tester.run_interactive_test()
    elif choice == "2":
        await tester.run_batch_test()
    elif choice == "3":
        print("可用平台:", ", ".join(tester.test_configs.keys()))
        platforms_input = input("請輸入要測試的平台 (用逗號分隔): ").strip()
        platforms = [p.strip() for p in platforms_input.split(",") if p.strip()]
        if platforms:
            await tester.run_batch_test(platforms)
        else:
            print("未選擇任何平台")
    else:
        print("無效選擇")
    
    print("\n🏁 測試完成！")


if __name__ == "__main__":
    # 設置事件循環策略 (Windows 相容性)
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 運行測試
    asyncio.run(main())
