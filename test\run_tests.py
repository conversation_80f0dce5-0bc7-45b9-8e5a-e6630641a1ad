#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MediaCrawler 測試運行器
提供簡單的介面來運行各種測試
"""

import asyncio
import sys
import os
import subprocess
from datetime import datetime

# 添加專案根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TestRunner:
    """測試運行器"""
    
    def __init__(self):
        self.test_dir = os.path.dirname(os.path.abspath(__file__))
        self.project_root = os.path.dirname(self.test_dir)
    
    def print_header(self, title: str):
        """列印標題"""
        print("\n" + "="*60)
        print(f"  {title}")
        print("="*60)
    
    def print_menu(self):
        """列印主選單"""
        self.print_header("MediaCrawler 測試工具")
        print("請選擇要執行的測試:")
        print()
        print("1. 🔧 基礎功能測試 - 測試所有平台的初始化")
        print("2. 🚀 爬取功能測試 - 測試實際數據爬取 (需要登錄)")
        print("3. 📊 單平台快速測試 - 測試指定平台")
        print("4. 🛠️  系統環境檢查 - 檢查依賴和配置")
        print("5. 📋 查看測試報告 - 查看歷史測試結果")
        print("0. 退出")
        print()
    
    async def run_basic_test(self):
        """運行基礎功能測試"""
        self.print_header("基礎功能測試")
        print("🔧 正在測試所有平台的基礎功能...")
        
        try:
            # 運行基礎測試
            from test_platform_crawlers import main as basic_test_main
            await basic_test_main()
        except Exception as e:
            print(f"❌ 基礎測試失敗: {str(e)}")
    
    async def run_crawl_test(self):
        """運行爬取功能測試"""
        self.print_header("爬取功能測試")
        print("🚀 正在啟動爬取功能測試...")
        print("⚠️  注意：此測試需要真實登錄各平台")
        
        try:
            from test_platform_crawl import main as crawl_test_main
            await crawl_test_main()
        except Exception as e:
            print(f"❌ 爬取測試失敗: {str(e)}")
    
    async def run_single_platform_test(self):
        """運行單平台測試"""
        self.print_header("單平台快速測試")
        
        platforms = {
            "1": ("xhs", "小紅書"),
            "2": ("dy", "抖音"),
            "3": ("ks", "快手"),
            "4": ("bili", "B站"),
            "5": ("wb", "微博"),
            "6": ("tieba", "貼吧"),
            "7": ("zhihu", "知乎")
        }
        
        print("請選擇要測試的平台:")
        for key, (code, name) in platforms.items():
            print(f"{key}. {name} ({code})")
        
        choice = input("\n請輸入選擇 (1-7): ").strip()
        
        if choice in platforms:
            platform_code, platform_name = platforms[choice]
            print(f"\n🎯 正在測試 {platform_name} 平台...")
            
            # 運行單平台測試
            await self.test_single_platform(platform_code, platform_name)
        else:
            print("❌ 無效選擇")
    
    async def test_single_platform(self, platform_code: str, platform_name: str):
        """測試單個平台"""
        try:
            print(f"🔧 正在初始化 {platform_name} 爬蟲...")
            
            # 這裡可以添加實際的單平台測試邏輯
            cmd = [
                sys.executable, "main.py",
                "--platform", platform_code,
                "--lt", "qrcode",
                "--type", "search",
                "--keywords", "測試",
                "--save_data_option", "json",
                "--get_comment", "false"
            ]
            
            print(f"📝 執行命令: {' '.join(cmd)}")
            print("⚠️  注意：需要掃描二維碼登錄")
            print("💡 按 Ctrl+C 可以中斷測試")
            
            # 詢問是否繼續
            if input("\n是否繼續執行？(y/n): ").lower().strip() not in ['y', 'yes']:
                print("測試已取消")
                return
            
            # 切換到專案根目錄執行
            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            print("\n🚀 測試開始...")
            print("-" * 50)
            
            # 實時顯示輸出
            try:
                for line in process.stdout:
                    print(line.rstrip())
            except KeyboardInterrupt:
                print("\n⏹️  用戶中斷測試")
                process.terminate()
            
            process.wait()
            
            if process.returncode == 0:
                print("\n✅ 測試完成")
            else:
                print(f"\n❌ 測試失敗 (退出碼: {process.returncode})")
                
        except Exception as e:
            print(f"❌ 測試過程中發生錯誤: {str(e)}")
    
    def check_system_environment(self):
        """檢查系統環境"""
        self.print_header("系統環境檢查")
        
        checks = [
            ("Python 版本", self.check_python_version),
            ("依賴套件", self.check_dependencies),
            ("配置文件", self.check_config_files),
            ("瀏覽器驅動", self.check_browser_drivers),
            ("數據目錄", self.check_data_directories)
        ]
        
        results = {}
        
        for check_name, check_func in checks:
            print(f"\n🔍 檢查 {check_name}...")
            try:
                result = check_func()
                results[check_name] = result
                status = "✅ 通過" if result else "❌ 失敗"
                print(f"   {status}")
            except Exception as e:
                results[check_name] = False
                print(f"   ❌ 錯誤: {str(e)}")
        
        # 總結
        passed = sum(1 for r in results.values() if r)
        total = len(results)
        
        print(f"\n📊 檢查結果: {passed}/{total} 項通過")
        
        if passed == total:
            print("🎉 系統環境完全正常！")
        else:
            print("⚠️  部分檢查未通過，可能影響功能使用")
    
    def check_python_version(self) -> bool:
        """檢查 Python 版本"""
        version = sys.version_info
        print(f"   Python {version.major}.{version.minor}.{version.micro}")
        return version >= (3, 9)
    
    def check_dependencies(self) -> bool:
        """檢查依賴套件"""
        try:
            import playwright
            import httpx
            import tenacity
            print(f"   Playwright: {playwright.__version__}")
            return True
        except ImportError as e:
            print(f"   缺少依賴: {str(e)}")
            return False
    
    def check_config_files(self) -> bool:
        """檢查配置文件"""
        config_files = [
            "config/base_config.py",
            "config/db_config.py"
        ]
        
        for config_file in config_files:
            file_path = os.path.join(self.project_root, config_file)
            if not os.path.exists(file_path):
                print(f"   缺少配置文件: {config_file}")
                return False
        
        print(f"   配置文件完整")
        return True
    
    def check_browser_drivers(self) -> bool:
        """檢查瀏覽器驅動"""
        try:
            from playwright.sync_api import sync_playwright
            with sync_playwright() as p:
                browsers = []
                if p.chromium.executable_path:
                    browsers.append("Chromium")
                if p.firefox.executable_path:
                    browsers.append("Firefox")
                if p.webkit.executable_path:
                    browsers.append("Webkit")
                
                print(f"   可用瀏覽器: {', '.join(browsers)}")
                return len(browsers) > 0
        except Exception as e:
            print(f"   瀏覽器驅動檢查失敗: {str(e)}")
            return False
    
    def check_data_directories(self) -> bool:
        """檢查數據目錄"""
        data_dirs = ["data", "browser_data"]
        
        for data_dir in data_dirs:
            dir_path = os.path.join(self.project_root, data_dir)
            if not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path)
                    print(f"   創建目錄: {data_dir}")
                except Exception as e:
                    print(f"   無法創建目錄 {data_dir}: {str(e)}")
                    return False
        
        print(f"   數據目錄正常")
        return True
    
    def view_test_reports(self):
        """查看測試報告"""
        self.print_header("測試報告")
        
        # 查找測試報告文件
        report_files = []
        for file in os.listdir(self.test_dir):
            if file.startswith(("platform_test_report_", "crawl_test_report_")):
                report_files.append(file)
        
        if not report_files:
            print("📭 沒有找到測試報告文件")
            return
        
        # 按時間排序
        report_files.sort(reverse=True)
        
        print(f"📋 找到 {len(report_files)} 個測試報告:")
        for i, file in enumerate(report_files[:10], 1):  # 只顯示最近10個
            file_path = os.path.join(self.test_dir, file)
            mtime = os.path.getmtime(file_path)
            time_str = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
            print(f"{i:2d}. {file} ({time_str})")
        
        # 詢問是否查看某個報告
        choice = input("\n輸入編號查看報告內容 (直接按 Enter 跳過): ").strip()
        
        if choice.isdigit() and 1 <= int(choice) <= len(report_files):
            file_to_view = report_files[int(choice) - 1]
            self.view_report_content(file_to_view)
    
    def view_report_content(self, filename: str):
        """查看報告內容"""
        file_path = os.path.join(self.test_dir, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 報告內容 ({filename}):")
            print("-" * 60)
            print(content)
            print("-" * 60)
            
        except Exception as e:
            print(f"❌ 無法讀取報告: {str(e)}")
    
    async def run(self):
        """運行測試工具"""
        while True:
            self.print_menu()
            
            choice = input("請輸入選擇 (0-5): ").strip()
            
            if choice == "0":
                print("👋 再見！")
                break
            elif choice == "1":
                await self.run_basic_test()
            elif choice == "2":
                await self.run_crawl_test()
            elif choice == "3":
                await self.run_single_platform_test()
            elif choice == "4":
                self.check_system_environment()
            elif choice == "5":
                self.view_test_reports()
            else:
                print("❌ 無效選擇，請重新輸入")
            
            # 等待用戶按鍵繼續
            input("\n按 Enter 鍵繼續...")


async def main():
    """主函數"""
    runner = TestRunner()
    await runner.run()


if __name__ == "__main__":
    # 設置事件循環策略 (Windows 相容性)
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 運行測試工具
    asyncio.run(main())
