#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告管理器
负责管理测试报告的生成、存储和查询
"""

import os
import json
import glob
from typing import Dict, List, Any, Optional
from datetime import datetime
import pandas as pd


class ReportManager:
    """报告管理器类"""
    
    def __init__(self):
        self.reports_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "reports")
        self._ensure_reports_dir()
    
    def _ensure_reports_dir(self):
        """确保报告目录存在"""
        os.makedirs(self.reports_dir, exist_ok=True)
    
    def save_test_report(self, test_results: Dict[str, Any], report_type: str = "quick_test") -> str:
        """保存测试报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_id = f"{report_type}_{timestamp}"
        
        # 准备报告数据
        report_data = {
            "id": report_id,
            "type": report_type,
            "timestamp": datetime.now().isoformat(),
            "results": test_results,
            "summary": self._generate_summary(test_results),
            "metadata": {
                "version": "1.0.0",
                "generator": "MediaCrawler Streamlit"
            }
        }
        
        # 保存 JSON 格式
        json_file = os.path.join(self.reports_dir, f"{report_id}.json")
        try:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"保存 JSON 报告失败: {str(e)}")
        
        # 保存文本格式
        txt_file = os.path.join(self.reports_dir, f"{report_id}.txt")
        try:
            self._save_text_report(report_data, txt_file)
        except Exception as e:
            print(f"保存文本报告失败: {str(e)}")
        
        return report_id
    
    def _generate_summary(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成测试摘要"""
        summary = {
            "total_platforms": 0,
            "passed": 0,
            "failed": 0,
            "partial": 0,
            "success_rate": 0.0,
            "total_duration": 0.0,
            "avg_duration": 0.0
        }
        
        if "platforms" in test_results:
            platforms = test_results["platforms"]
            summary["total_platforms"] = len(platforms)
            
            total_duration = 0.0
            for platform_result in platforms.values():
                status = platform_result.get("status", "")
                duration = platform_result.get("duration", 0)
                total_duration += duration
                
                if status == "通过" or status == "完成":
                    summary["passed"] += 1
                elif status == "失败" or status == "错误":
                    summary["failed"] += 1
                else:
                    summary["partial"] += 1
            
            if summary["total_platforms"] > 0:
                summary["success_rate"] = summary["passed"] / summary["total_platforms"] * 100
                summary["avg_duration"] = total_duration / summary["total_platforms"]
            
            summary["total_duration"] = total_duration
        
        elif "summary" in test_results:
            # 如果结果中已经包含摘要，直接使用
            summary.update(test_results["summary"])
        
        return summary
    
    def _save_text_report(self, report_data: Dict[str, Any], file_path: str):
        """保存文本格式报告"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("MediaCrawler 测试报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"报告ID: {report_data['id']}\n")
            f.write(f"测试类型: {report_data['type']}\n")
            f.write(f"生成时间: {report_data['timestamp']}\n\n")
            
            # 写入摘要
            summary = report_data.get("summary", {})
            f.write("测试摘要:\n")
            f.write("-" * 30 + "\n")
            f.write(f"总平台数: {summary.get('total_platforms', 0)}\n")
            f.write(f"通过: {summary.get('passed', 0)}\n")
            f.write(f"失败: {summary.get('failed', 0)}\n")
            f.write(f"部分通过: {summary.get('partial', 0)}\n")
            f.write(f"成功率: {summary.get('success_rate', 0):.1f}%\n")
            f.write(f"总耗时: {summary.get('total_duration', 0):.2f}秒\n\n")
            
            # 写入详细结果
            results = report_data.get("results", {})
            if "platforms" in results:
                f.write("详细结果:\n")
                f.write("-" * 30 + "\n")
                for platform_key, platform_result in results["platforms"].items():
                    f.write(f"平台: {platform_result.get('name', platform_key)}\n")
                    f.write(f"状态: {platform_result.get('status', '未知')}\n")
                    f.write(f"耗时: {platform_result.get('duration', 0):.2f}秒\n")
                    if platform_result.get('error'):
                        f.write(f"错误: {platform_result['error']}\n")
                    f.write("-" * 20 + "\n")
    
    def get_recent_reports(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的测试报告"""
        reports = []
        
        # 查找所有 JSON 报告文件
        json_files = glob.glob(os.path.join(self.reports_dir, "*.json"))
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)
                
                # 提取基本信息
                report_info = {
                    "id": report_data.get("id", ""),
                    "name": os.path.basename(json_file),
                    "type": report_data.get("type", "unknown"),
                    "date": report_data.get("timestamp", ""),
                    "status": self._get_report_status(report_data),
                    "platform_count": report_data.get("summary", {}).get("total_platforms", 0),
                    "success_rate": report_data.get("summary", {}).get("success_rate", 0),
                    "file_path": json_file
                }
                
                reports.append(report_info)
                
            except Exception as e:
                print(f"读取报告文件失败 {json_file}: {str(e)}")
        
        # 按时间排序
        reports.sort(key=lambda x: x["date"], reverse=True)
        
        return reports[:limit]
    
    def _get_report_status(self, report_data: Dict[str, Any]) -> str:
        """获取报告状态"""
        summary = report_data.get("summary", {})
        success_rate = summary.get("success_rate", 0)
        
        if success_rate >= 90:
            return "优秀"
        elif success_rate >= 70:
            return "良好"
        elif success_rate >= 50:
            return "一般"
        else:
            return "需要改进"
    
    def load_report(self, report_id: str) -> Optional[Dict[str, Any]]:
        """加载指定的测试报告"""
        json_file = os.path.join(self.reports_dir, f"{report_id}.json")
        
        if not os.path.exists(json_file):
            return None
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载报告失败: {str(e)}")
            return None
    
    def delete_report(self, report_id: str) -> bool:
        """删除测试报告"""
        try:
            json_file = os.path.join(self.reports_dir, f"{report_id}.json")
            txt_file = os.path.join(self.reports_dir, f"{report_id}.txt")
            
            if os.path.exists(json_file):
                os.remove(json_file)
            
            if os.path.exists(txt_file):
                os.remove(txt_file)
            
            return True
        except Exception as e:
            print(f"删除报告失败: {str(e)}")
            return False
    
    def get_report_statistics(self) -> Dict[str, Any]:
        """获取报告统计信息"""
        reports = self.get_recent_reports(limit=100)  # 获取最近100个报告
        
        stats = {
            "total_reports": len(reports),
            "report_types": {},
            "success_rate_trend": [],
            "platform_performance": {},
            "recent_activity": []
        }
        
        # 统计报告类型
        for report in reports:
            report_type = report["type"]
            if report_type not in stats["report_types"]:
                stats["report_types"][report_type] = 0
            stats["report_types"][report_type] += 1
        
        # 成功率趋势（最近10个报告）
        recent_reports = reports[:10]
        stats["success_rate_trend"] = [
            {
                "date": report["date"][:10],  # 只取日期部分
                "success_rate": report["success_rate"]
            }
            for report in recent_reports
        ]
        
        # 最近活动
        stats["recent_activity"] = [
            {
                "date": report["date"],
                "type": report["type"],
                "status": report["status"],
                "platforms": report["platform_count"]
            }
            for report in reports[:5]
        ]
        
        return stats
    
    def export_report_csv(self, report_ids: List[str] = None) -> str:
        """导出报告为 CSV 格式"""
        if report_ids is None:
            reports = self.get_recent_reports(limit=100)
            report_ids = [report["id"] for report in reports]
        
        data = []
        for report_id in report_ids:
            report_data = self.load_report(report_id)
            if report_data:
                summary = report_data.get("summary", {})
                data.append({
                    "报告ID": report_data.get("id", ""),
                    "类型": report_data.get("type", ""),
                    "时间": report_data.get("timestamp", ""),
                    "总平台数": summary.get("total_platforms", 0),
                    "通过数": summary.get("passed", 0),
                    "失败数": summary.get("failed", 0),
                    "成功率": summary.get("success_rate", 0),
                    "总耗时": summary.get("total_duration", 0)
                })
        
        if data:
            df = pd.DataFrame(data)
            csv_file = os.path.join(self.reports_dir, f"reports_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            return csv_file
        
        return ""
