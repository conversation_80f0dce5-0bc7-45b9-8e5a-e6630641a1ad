#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试页面
提供一键测试所有平台的功能
"""

import streamlit as st
import asyncio
import sys
import os
from datetime import datetime
import time

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from test.utils.test_runner import TestRunner
from test.utils.config_manager import ConfigManager
from test.utils.report_manager import ReportManager

# 页面配置
st.set_page_config(
    page_title="快速测试 - MediaCrawler",
    page_icon="🔧",
    layout="wide"
)

def init_session_state():
    """初始化 session state"""
    if 'test_runner' not in st.session_state:
        st.session_state.test_runner = TestRunner()
    
    if 'config_manager' not in st.session_state:
        st.session_state.config_manager = ConfigManager()
    
    if 'report_manager' not in st.session_state:
        st.session_state.report_manager = ReportManager()
    
    if 'test_running' not in st.session_state:
        st.session_state.test_running = False
    
    if 'test_results' not in st.session_state:
        st.session_state.test_results = None
    
    if 'test_progress' not in st.session_state:
        st.session_state.test_progress = []

def render_header():
    """渲染页面头部"""
    st.title("🔧 快速测试")
    st.markdown("---")
    
    st.markdown("""
    快速测试功能可以一键检查所有平台的基本功能，包括：
    - 模块导入检查
    - 爬虫实例创建
    - 必要方法验证
    - 基础配置检查
    """)

def render_test_config():
    """渲染测试配置"""
    st.subheader("📋 测试配置")
    
    # 获取平台列表
    platforms = st.session_state.test_runner.platforms
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("**选择要测试的平台：**")
        
        # 全选/取消全选
        col_all, col_none = st.columns(2)
        with col_all:
            if st.button("全选", use_container_width=True):
                for platform_key in platforms.keys():
                    st.session_state[f"platform_{platform_key}"] = True
        
        with col_none:
            if st.button("取消全选", use_container_width=True):
                for platform_key in platforms.keys():
                    st.session_state[f"platform_{platform_key}"] = False
        
        # 平台选择
        selected_platforms = []
        
        # 创建两列布局显示平台
        platform_items = list(platforms.items())
        mid_point = len(platform_items) // 2 + 1

        col_left, col_right = st.columns(2)

        with col_left:
            for platform_key, platform_info in platform_items[:mid_point]:
                key = f"platform_{platform_key}"
                if key not in st.session_state:
                    st.session_state[key] = True

                if st.session_state[key]:
                    selected_platforms.append(platform_key)

                st.checkbox(
                    f"{platform_info['icon']} {platform_info['name']} ({platform_key})",
                    value=st.session_state[key],
                    key=key
                )

        with col_right:
            for platform_key, platform_info in platform_items[mid_point:]:
                key = f"platform_{platform_key}"
                if key not in st.session_state:
                    st.session_state[key] = True

                if st.session_state[key]:
                    selected_platforms.append(platform_key)

                st.checkbox(
                    f"{platform_info['icon']} {platform_info['name']} ({platform_key})",
                    value=st.session_state[key],
                    key=key
                )
    
    with col2:
        st.markdown("**测试选项：**")
        
        # 测试超时设置
        timeout = st.slider(
            "测试超时 (秒)",
            min_value=10,
            max_value=120,
            value=30,
            step=5
        )
        
        # 并发测试
        concurrent = st.checkbox("启用并发测试", value=False)
        
        # 详细日志
        verbose = st.checkbox("显示详细日志", value=True)
        
        # 自动保存报告
        auto_save = st.checkbox("自动保存报告", value=True)
    
    return selected_platforms, {
        "timeout": timeout,
        "concurrent": concurrent,
        "verbose": verbose,
        "auto_save": auto_save
    }

async def run_test_async(selected_platforms, config):
    """异步运行测试"""
    progress_messages = []
    
    def progress_callback(message):
        progress_messages.append({
            "time": datetime.now().strftime("%H:%M:%S"),
            "message": message
        })
        st.session_state.test_progress = progress_messages
    
    # 运行测试
    results = await st.session_state.test_runner.run_quick_test(
        selected_platforms, 
        progress_callback
    )
    
    return results

def render_test_execution():
    """渲染测试执行区域"""
    st.subheader("🚀 测试执行")
    
    selected_platforms, config = render_test_config()
    
    if not selected_platforms:
        st.warning("请至少选择一个平台进行测试")
        return
    
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button(
            "开始测试", 
            type="primary", 
            use_container_width=True,
            disabled=st.session_state.test_running
        ):
            st.session_state.test_running = True
            st.session_state.test_progress = []
            st.session_state.test_results = None
            st.rerun()
    
    with col2:
        if st.button(
            "停止测试", 
            use_container_width=True,
            disabled=not st.session_state.test_running
        ):
            st.session_state.test_running = False
            st.rerun()
    
    with col3:
        if st.session_state.test_running:
            st.info(f"正在测试 {len(selected_platforms)} 个平台...")
        elif st.session_state.test_results:
            summary = st.session_state.test_results.get("summary", {})
            st.success(f"测试完成！通过率: {summary.get('passed', 0)}/{summary.get('total', 0)}")
    
    # 执行测试
    if st.session_state.test_running and not st.session_state.test_results:
        with st.spinner("正在执行测试..."):
            try:
                # 由于 Streamlit 的限制，这里使用同步方式模拟异步测试
                results = run_sync_test(selected_platforms, config)
                st.session_state.test_results = results
                st.session_state.test_running = False
                
                # 自动保存报告
                if config.get("auto_save", True):
                    report_id = st.session_state.report_manager.save_test_report(
                        results, "quick_test"
                    )
                    st.success(f"测试报告已保存: {report_id}")
                
                st.rerun()
                
            except Exception as e:
                st.error(f"测试执行失败: {str(e)}")
                st.session_state.test_running = False

def run_sync_test(selected_platforms, config):
    """同步方式运行测试（适配 Streamlit）"""
    results = {
        "test_type": "quick_test",
        "start_time": datetime.now(),
        "end_time": None,
        "total_platforms": len(selected_platforms),
        "platforms": {},
        "summary": {
            "total": len(selected_platforms),
            "passed": 0,
            "failed": 0,
            "partial": 0
        }
    }
    
    progress_bar = st.progress(0)
    status_text = st.empty()
    log_container = st.empty()
    
    logs = []
    
    for i, platform_key in enumerate(selected_platforms):
        platform_info = st.session_state.test_runner.platforms[platform_key]
        
        # 更新进度
        progress = (i + 1) / len(selected_platforms)
        progress_bar.progress(progress)
        status_text.text(f"正在测试 {platform_info['name']} ({i+1}/{len(selected_platforms)})")
        
        # 模拟测试过程
        log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] 开始测试 {platform_info['name']}"
        logs.append(log_msg)
        
        if config.get("verbose", True):
            log_container.text_area("测试日志", "\n".join(logs[-10:]), height=150)
        
        # 执行实际测试
        try:
            crawler = platform_info["crawler"]()
            
            details = {
                "has_start": hasattr(crawler, 'start'),
                "has_search": hasattr(crawler, 'search'),
                "has_launch_browser": hasattr(crawler, 'launch_browser'),
                "instance_created": True
            }
            
            if all(details.values()):
                status = "通过"
                results["summary"]["passed"] += 1
                log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] ✅ {platform_info['name']} 测试通过"
            else:
                status = "部分通过"
                results["summary"]["partial"] += 1
                log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ {platform_info['name']} 部分功能缺失"
            
            results["platforms"][platform_key] = {
                "platform": platform_key,
                "name": platform_info["name"],
                "status": status,
                "details": details,
                "start_time": datetime.now(),
                "end_time": datetime.now(),
                "duration": 0.5,
                "error": None
            }
            
        except Exception as e:
            status = "失败"
            results["summary"]["failed"] += 1
            log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] ❌ {platform_info['name']} 测试失败: {str(e)}"
            
            results["platforms"][platform_key] = {
                "platform": platform_key,
                "name": platform_info["name"],
                "status": status,
                "details": {},
                "start_time": datetime.now(),
                "end_time": datetime.now(),
                "duration": 0.5,
                "error": str(e)
            }
        
        logs.append(log_msg)
        
        if config.get("verbose", True):
            log_container.text_area("测试日志", "\n".join(logs[-10:]), height=150)
        
        # 短暂延迟
        time.sleep(0.5)
    
    results["end_time"] = datetime.now()
    results["total_duration"] = (results["end_time"] - results["start_time"]).total_seconds()
    
    progress_bar.progress(1.0)
    status_text.text("测试完成！")
    
    return results

def render_test_results():
    """渲染测试结果"""
    if not st.session_state.test_results:
        return
    
    st.subheader("📊 测试结果")
    
    results = st.session_state.test_results
    summary = results.get("summary", {})
    
    # 显示摘要
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总平台数", summary.get("total", 0))
    
    with col2:
        st.metric("通过", summary.get("passed", 0), delta=f"{summary.get('passed', 0)}/{summary.get('total', 1)*100:.1f}%")
    
    with col3:
        st.metric("失败", summary.get("failed", 0))
    
    with col4:
        st.metric("部分通过", summary.get("partial", 0))
    
    # 详细结果表格
    st.markdown("### 详细结果")
    
    platforms_data = []
    for platform_key, platform_result in results.get("platforms", {}).items():
        platforms_data.append({
            "平台": platform_result.get("name", platform_key),
            "状态": platform_result.get("status", "未知"),
            "耗时": f"{platform_result.get('duration', 0):.2f}s",
            "start方法": "✅" if platform_result.get("details", {}).get("has_start", False) else "❌",
            "search方法": "✅" if platform_result.get("details", {}).get("has_search", False) else "❌",
            "launch_browser方法": "✅" if platform_result.get("details", {}).get("has_launch_browser", False) else "❌",
            "错误信息": platform_result.get("error", "") or "无"
        })
    
    if platforms_data:
        st.dataframe(platforms_data, use_container_width=True)
    
    # 操作按钮
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("保存报告", use_container_width=True):
            report_id = st.session_state.report_manager.save_test_report(
                results, "quick_test"
            )
            st.success(f"报告已保存: {report_id}")
    
    with col2:
        if st.button("重新测试", use_container_width=True):
            st.session_state.test_results = None
            st.session_state.test_progress = []
            st.rerun()
    
    with col3:
        if st.button("查看报告", use_container_width=True):
            st.switch_page("pages/04_📋_测试报告.py")

def main():
    """主函数"""
    init_session_state()
    
    render_header()
    render_test_execution()
    render_test_results()

if __name__ == "__main__":
    main()
