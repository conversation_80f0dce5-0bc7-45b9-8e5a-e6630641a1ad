#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试报告页面
提供测试报告的查看、管理和分析功能
"""

import streamlit as st
import sys
import os
import json
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from test.utils.report_manager import ReportManager

# 页面配置
st.set_page_config(
    page_title="测试报告 - MediaCrawler",
    page_icon="📋",
    layout="wide"
)

def init_session_state():
    """初始化 session state"""
    if 'report_manager' not in st.session_state:
        st.session_state.report_manager = ReportManager()

def render_header():
    """渲染页面头部"""
    st.title("📋 测试报告")
    st.markdown("---")
    
    st.markdown("""
    测试报告功能提供：
    - 📊 报告统计和分析
    - 📄 历史报告查看
    - 📈 趋势分析
    - 💾 报告导出
    """)

def render_report_statistics():
    """渲染报告统计"""
    st.subheader("📊 报告统计")
    
    stats = st.session_state.report_manager.get_report_statistics()
    
    # 基础统计
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总报告数", stats.get("total_reports", 0))
    
    with col2:
        recent_reports = len([r for r in stats.get("recent_activity", []) 
                            if datetime.fromisoformat(r["date"].replace('Z', '+00:00')).date() >= 
                            (datetime.now() - timedelta(days=7)).date()])
        st.metric("本周报告", recent_reports)
    
    with col3:
        avg_success = 0
        if stats.get("success_rate_trend"):
            avg_success = sum(r["success_rate"] for r in stats["success_rate_trend"]) / len(stats["success_rate_trend"])
        st.metric("平均成功率", f"{avg_success:.1f}%")
    
    with col4:
        report_types = len(stats.get("report_types", {}))
        st.metric("报告类型", report_types)
    
    # 报告类型分布
    if stats.get("report_types"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 报告类型分布")
            
            # 创建饼图
            fig_pie = px.pie(
                values=list(stats["report_types"].values()),
                names=list(stats["report_types"].keys()),
                title="报告类型分布"
            )
            st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            st.markdown("#### 成功率趋势")
            
            if stats.get("success_rate_trend"):
                # 创建趋势图
                trend_data = stats["success_rate_trend"]
                fig_line = px.line(
                    x=[item["date"] for item in trend_data],
                    y=[item["success_rate"] for item in trend_data],
                    title="最近测试成功率趋势",
                    labels={"x": "日期", "y": "成功率 (%)"}
                )
                fig_line.update_layout(showlegend=False)
                st.plotly_chart(fig_line, use_container_width=True)
            else:
                st.info("暂无趋势数据")

def render_recent_reports():
    """渲染最近报告"""
    st.subheader("📄 最近报告")
    
    # 获取报告列表
    reports = st.session_state.report_manager.get_recent_reports(limit=20)
    
    if not reports:
        st.info("暂无测试报告")
        return
    
    # 筛选选项
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # 报告类型筛选
        report_types = list(set(r["type"] for r in reports))
        selected_type = st.selectbox("报告类型", ["全部"] + report_types)
    
    with col2:
        # 状态筛选
        statuses = list(set(r["status"] for r in reports))
        selected_status = st.selectbox("状态", ["全部"] + statuses)
    
    with col3:
        # 时间范围
        time_range = st.selectbox("时间范围", ["全部", "今天", "本周", "本月"])
    
    # 应用筛选
    filtered_reports = reports
    
    if selected_type != "全部":
        filtered_reports = [r for r in filtered_reports if r["type"] == selected_type]
    
    if selected_status != "全部":
        filtered_reports = [r for r in filtered_reports if r["status"] == selected_status]
    
    if time_range != "全部":
        now = datetime.now()
        if time_range == "今天":
            cutoff = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif time_range == "本周":
            cutoff = now - timedelta(days=7)
        elif time_range == "本月":
            cutoff = now - timedelta(days=30)
        
        filtered_reports = [
            r for r in filtered_reports 
            if datetime.fromisoformat(r["date"].replace('Z', '+00:00')) >= cutoff
        ]
    
    # 显示报告列表
    if filtered_reports:
        # 创建表格数据
        table_data = []
        for report in filtered_reports:
            table_data.append({
                "报告名称": report["name"],
                "类型": report["type"],
                "日期": report["date"][:19].replace('T', ' '),
                "状态": report["status"],
                "平台数": report["platform_count"],
                "成功率": f"{report['success_rate']:.1f}%",
                "ID": report["id"]
            })
        
        df = pd.DataFrame(table_data)
        
        # 使用 dataframe 显示，支持选择
        event = st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            on_select="rerun",
            selection_mode="single-row"
        )
        
        # 处理选择事件
        if event.selection.rows:
            selected_idx = event.selection.rows[0]
            selected_report = filtered_reports[selected_idx]
            st.session_state.selected_report_id = selected_report["id"]
    
    else:
        st.warning("没有符合条件的报告")

def render_report_details():
    """渲染报告详情"""
    if not hasattr(st.session_state, 'selected_report_id'):
        st.info("请从上方列表中选择一个报告查看详情")
        return
    
    report_id = st.session_state.selected_report_id
    report_data = st.session_state.report_manager.load_report(report_id)
    
    if not report_data:
        st.error("无法加载报告数据")
        return
    
    st.subheader(f"📄 报告详情: {report_id}")
    
    # 基本信息
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.info(f"**类型**: {report_data.get('type', '未知')}")
    
    with col2:
        st.info(f"**时间**: {report_data.get('timestamp', '未知')[:19]}")
    
    with col3:
        summary = report_data.get('summary', {})
        st.info(f"**成功率**: {summary.get('success_rate', 0):.1f}%")
    
    # 摘要信息
    st.markdown("#### 📊 测试摘要")
    
    summary = report_data.get('summary', {})
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总平台数", summary.get('total_platforms', 0))
    
    with col2:
        st.metric("通过", summary.get('passed', 0))
    
    with col3:
        st.metric("失败", summary.get('failed', 0))
    
    with col4:
        st.metric("总耗时", f"{summary.get('total_duration', 0):.2f}s")
    
    # 详细结果
    results = report_data.get('results', {})
    if 'platforms' in results:
        st.markdown("#### 📋 平台测试结果")
        
        platforms_data = []
        for platform_key, platform_result in results['platforms'].items():
            platforms_data.append({
                "平台": platform_result.get('name', platform_key),
                "状态": platform_result.get('status', '未知'),
                "耗时": f"{platform_result.get('duration', 0):.2f}s",
                "错误": platform_result.get('error', '') or '无'
            })
        
        if platforms_data:
            df_platforms = pd.DataFrame(platforms_data)
            st.dataframe(df_platforms, use_container_width=True, hide_index=True)
        
        # 可视化结果
        if len(platforms_data) > 1:
            st.markdown("#### 📈 结果可视化")
            
            # 状态分布
            status_counts = {}
            for item in platforms_data:
                status = item["状态"]
                status_counts[status] = status_counts.get(status, 0) + 1
            
            fig_status = px.bar(
                x=list(status_counts.keys()),
                y=list(status_counts.values()),
                title="平台测试状态分布",
                labels={"x": "状态", "y": "数量"}
            )
            st.plotly_chart(fig_status, use_container_width=True)
    
    # 操作按钮
    st.markdown("#### 🔧 操作")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("📥 下载 JSON", use_container_width=True):
            st.download_button(
                label="下载 JSON 报告",
                data=json.dumps(report_data, ensure_ascii=False, indent=2),
                file_name=f"{report_id}.json",
                mime="application/json"
            )
    
    with col2:
        if st.button("📄 查看原始数据", use_container_width=True):
            with st.expander("原始 JSON 数据", expanded=False):
                st.json(report_data)
    
    with col3:
        if st.button("🔄 重新加载", use_container_width=True):
            st.rerun()
    
    with col4:
        if st.button("🗑️ 删除报告", use_container_width=True, type="secondary"):
            if st.session_state.report_manager.delete_report(report_id):
                st.success("报告已删除")
                if hasattr(st.session_state, 'selected_report_id'):
                    delattr(st.session_state, 'selected_report_id')
                st.rerun()
            else:
                st.error("删除失败")

def render_export_options():
    """渲染导出选项"""
    st.subheader("💾 导出选项")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 批量导出")
        
        export_format = st.selectbox("导出格式", ["CSV", "JSON"])
        export_count = st.slider("导出数量", 1, 50, 10)
        
        if st.button("导出报告", use_container_width=True):
            if export_format == "CSV":
                csv_file = st.session_state.report_manager.export_report_csv()
                if csv_file:
                    st.success(f"CSV 文件已生成: {os.path.basename(csv_file)}")
                else:
                    st.error("导出失败")
            else:
                st.info("JSON 批量导出功能开发中...")
    
    with col2:
        st.markdown("#### 清理选项")
        
        st.warning("⚠️ 危险操作")
        
        days_old = st.slider("删除多少天前的报告", 7, 90, 30)
        
        if st.button("清理旧报告", use_container_width=True, type="secondary"):
            st.warning("此功能需要确认后才能执行")

def main():
    """主函数"""
    init_session_state()
    
    render_header()
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📊 统计概览", "📄 报告列表", "📋 报告详情", "💾 导出管理"])
    
    with tab1:
        render_report_statistics()
    
    with tab2:
        render_recent_reports()
    
    with tab3:
        render_report_details()
    
    with tab4:
        render_export_options()

if __name__ == "__main__":
    main()
