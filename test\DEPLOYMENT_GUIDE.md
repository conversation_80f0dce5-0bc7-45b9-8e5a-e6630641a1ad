# MediaCrawler Streamlit Web 测试工具部署指南

## 🎯 项目概述

MediaCrawler Streamlit Web 测试工具提供了两个版本：
1. **独立版本** - 稳定、快速、不依赖原始模块
2. **完整版本** - 功能完整、需要原始模块支持

## 🚀 快速部署

### 1. 环境准备

```bash
# 确保 Python 3.9+
python --version

# 安装 Streamlit 依赖
uv add streamlit plotly pandas
# 或者
pip install streamlit plotly pandas
```

### 2. 启动应用

#### 推荐方式：独立版本

```bash
cd test
python run_streamlit.py --standalone --port 8503
```

访问: http://localhost:8503

#### 完整版本（如果环境支持）

```bash
cd test
python run_streamlit.py --port 8501
```

访问: http://localhost:8501

## 📁 文件结构

```
test/
├── streamlit_app_standalone.py   # 独立版本 ⭐ 推荐
├── streamlit_app.py              # 完整版本
├── run_streamlit.py              # 启动脚本
├── streamlit_README.md           # 详细文档
├── DEPLOYMENT_GUIDE.md           # 本文件
├── pages/                        # 完整版本页面
│   ├── 01_🔧_快速测试.py
│   ├── 02_📊_平台测试.py
│   ├── 03_🚀_实际爬取.py
│   ├── 04_📋_测试报告.py
│   ├── 05_⚙️_系统设置.py
│   └── 06_📚_帮助文档.py
└── utils/                        # 工具模块
    ├── test_runner.py
    ├── config_manager.py
    └── report_manager.py
```

## 🔧 版本对比

| 特性 | 独立版本 | 完整版本 |
|------|----------|----------|
| 启动速度 | ⚡ 快速 | 🐌 较慢 |
| 稳定性 | 🟢 高 | 🟡 中等 |
| 功能完整性 | 🟡 基础 | 🟢 完整 |
| 依赖要求 | 🟢 低 | 🔴 高 |
| 推荐场景 | 快速验证、新用户 | 深度测试、高级用户 |

## 📊 功能说明

### 独立版本功能

1. **系统状态检查**
   - Python 环境检查
   - 依赖包状态
   - 配置文件检查
   - 主脚本检查

2. **平台测试**
   - 批量命令测试
   - 平台可用性检查
   - 错误诊断

3. **手动测试指导**
   - 命令生成器
   - 参数配置
   - 执行指导

### 完整版本功能

1. **所有独立版本功能**
2. **实际爬取测试**
3. **详细报告管理**
4. **可视化图表**
5. **配置管理**
6. **帮助文档**

## 🔍 故障排除

### 常见问题

1. **模块导入错误**
   ```
   解决方案: 使用独立版本
   命令: python run_streamlit.py --standalone
   ```

2. **端口被占用**
   ```
   解决方案: 更换端口
   命令: python run_streamlit.py --standalone --port 8504
   ```

3. **依赖缺失**
   ```bash
   # 重新安装依赖
   uv add streamlit plotly pandas
   ```

4. **路径问题**
   ```bash
   # 确保在正确目录
   cd MediaCrawler/test
   pwd  # 应该显示 .../MediaCrawler/test
   ```

### 调试模式

```bash
# 启用详细日志
python run_streamlit.py --standalone --dev

# 或者直接使用 streamlit
streamlit run streamlit_app_standalone.py --logger.level debug
```

## 🌐 网络配置

### 本地访问
```bash
python run_streamlit.py --standalone --host localhost --port 8503
```

### 局域网访问
```bash
python run_streamlit.py --standalone --host 0.0.0.0 --port 8503
```

### 自定义配置
```bash
# 完整配置示例
python run_streamlit.py \
  --standalone \
  --host 0.0.0.0 \
  --port 8503 \
  --dev
```

## 📈 性能优化

### 建议配置

1. **系统要求**
   - RAM: 2GB+ (独立版本) / 4GB+ (完整版本)
   - CPU: 双核以上
   - 网络: 稳定连接

2. **浏览器推荐**
   - Chrome (推荐)
   - Firefox
   - Edge

3. **优化设置**
   ```bash
   # 使用独立版本
   python run_streamlit.py --standalone
   
   # 限制并发
   export STREAMLIT_SERVER_MAX_UPLOAD_SIZE=200
   ```

## 🔒 安全考虑

### 生产环境部署

1. **网络安全**
   ```bash
   # 仅本地访问
   --host 127.0.0.1
   
   # 使用防火墙限制访问
   ```

2. **认证（可选）**
   - 可以在前端添加简单认证
   - 使用反向代理 (nginx) 添加认证

3. **HTTPS（可选）**
   ```bash
   # 使用 nginx 反向代理添加 SSL
   ```

## 📝 维护指南

### 日常维护

1. **日志检查**
   ```bash
   # 查看 Streamlit 日志
   tail -f ~/.streamlit/logs/streamlit.log
   ```

2. **清理缓存**
   ```bash
   # 清理 Streamlit 缓存
   streamlit cache clear
   ```

3. **更新依赖**
   ```bash
   # 更新到最新版本
   uv sync --upgrade
   ```

### 备份恢复

1. **配置备份**
   ```bash
   # 备份配置文件
   cp -r test/config test/config.backup
   ```

2. **报告备份**
   ```bash
   # 备份测试报告
   cp -r test/reports test/reports.backup
   ```

## 🎉 成功部署检查

### 验证步骤

1. **访问应用**
   - 打开浏览器访问 http://localhost:8503
   - 确认页面正常加载

2. **功能测试**
   - 点击 "系统状态" 标签页
   - 点击 "🔄 刷新状态" 按钮
   - 确认状态检查正常

3. **平台测试**
   - 切换到 "平台测试" 标签页
   - 选择一个平台进行测试
   - 确认测试结果正常

### 成功标志

- ✅ 页面正常加载
- ✅ 系统状态检查通过
- ✅ 平台测试功能正常
- ✅ 无错误信息

## 📞 技术支持

### 获取帮助

1. **查看文档**
   - 应用内帮助文档
   - streamlit_README.md

2. **问题反馈**
   - GitHub Issues
   - 项目讨论区

3. **社区支持**
   - MediaCrawler 社区
   - Streamlit 官方文档

---

**🎊 恭喜！您已成功部署 MediaCrawler Streamlit Web 测试工具！**

推荐从独立版本开始使用，熟悉后可以尝试完整版本的高级功能。
