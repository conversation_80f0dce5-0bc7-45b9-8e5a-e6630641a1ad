# MediaCrawler 測試工具

這個目錄包含了 MediaCrawler 專案的測試工具，用於測試所有7個支援平台的爬蟲功能。

## 📁 文件結構

```
test/
├── README.md                    # 本說明文件
├── run_tests.py                 # 主測試運行器 (推薦使用)
├── test_platform_crawlers.py    # 基礎功能測試
├── test_platform_crawl.py       # 實際爬取測試
├── test_expiring_local_cache.py # 快取測試 (原有)
├── test_proxy_ip_pool.py        # 代理測試 (原有)
├── test_redis_cache.py          # Redis 測試 (原有)
└── test_utils.py                # 工具測試 (原有)
```

## 🚀 快速開始

### 方法一：使用主測試運行器 (推薦)

```bash
# 進入專案根目錄
cd MediaCrawler

# 運行測試工具
uv run python test/run_tests.py
```

這將啟動一個互動式選單，您可以選擇不同的測試模式。

### 方法二：直接運行特定測試

```bash
# 基礎功能測試 (不需要登錄)
uv run python test/test_platform_crawlers.py

# 實際爬取測試 (需要登錄)
uv run python test/test_platform_crawl.py
```

## 🔧 測試類型說明

### 1. 基礎功能測試
- **文件**: `test_platform_crawlers.py`
- **用途**: 測試所有平台的初始化功能
- **特點**: 不需要登錄，快速檢查基本功能
- **適用場景**: 環境檢查、快速驗證

### 2. 實際爬取測試
- **文件**: `test_platform_crawl.py`
- **用途**: 測試真實的數據爬取功能
- **特點**: 需要掃描二維碼登錄各平台
- **適用場景**: 完整功能測試、數據爬取驗證

### 3. 主測試運行器
- **文件**: `run_tests.py`
- **用途**: 提供統一的測試介面
- **特點**: 互動式選單，支援多種測試模式
- **適用場景**: 日常測試、系統檢查

## 📊 支援的平台

| 平台代碼 | 平台名稱 | 測試關鍵詞 | 狀態 |
|---------|---------|-----------|------|
| xhs     | 小紅書   | 美食、旅行、穿搭 | ✅ |
| dy      | 抖音     | 搞笑、音樂、舞蹈 | ✅ |
| ks      | 快手     | 美食、生活、搞笑 | ✅ |
| bili    | B站      | 動畫、遊戲、科技 | ✅ |
| wb      | 微博     | 熱搜、新聞、娛樂 | ✅ |
| tieba   | 貼吧     | 遊戲、動漫、科技 | ✅ |
| zhihu   | 知乎     | 程式設計、科技、職場 | ✅ |

## 🎯 測試模式

### 互動模式
- 逐一測試每個平台
- 可以選擇跳過某些平台
- 適合首次測試或詳細檢查

### 批次模式
- 快速測試所有平台
- 跳過登錄步驟
- 適合環境驗證

### 自定義模式
- 選擇特定平台進行測試
- 靈活配置測試參數
- 適合針對性測試

## 📋 測試報告

測試完成後會自動生成報告文件：

- **JSON 格式**: `crawl_test_report_[模式]_[時間戳].json`
- **文本格式**: `crawl_test_report_[模式]_[時間戳].txt`

報告包含以下資訊：
- 測試狀態
- 爬取數據量
- 執行時間
- 錯誤詳情

## ⚙️ 配置說明

### 測試參數配置

在 `test_platform_crawl.py` 中可以調整測試參數：

```python
self.test_config = {
    "xhs": {
        "name": "小紅書",
        "keywords": ["美食推薦"],
        "max_notes": 5,           # 爬取數量
        "enable_comments": False  # 是否爬取評論
    },
    # ... 其他平台配置
}
```

### 環境要求

- Python 3.9+
- 已安裝所有依賴 (`uv sync`)
- 已安裝瀏覽器驅動 (`uv run playwright install`)
- 網路連接正常

## 🔍 故障排除

### 常見問題

1. **ImportError: No module named 'xxx'**
   ```bash
   # 重新安裝依賴
   uv sync
   ```

2. **瀏覽器驅動錯誤**
   ```bash
   # 重新安裝瀏覽器驅動
   uv run playwright install
   ```

3. **登錄失敗**
   - 檢查網路連接
   - 確認平台賬號狀態
   - 嘗試手動登錄網頁版

4. **數據爬取失敗**
   - 檢查關鍵詞是否合適
   - 確認平台反爬蟲策略變化
   - 調整爬取間隔時間

### 調試模式

如需詳細的調試資訊，可以修改配置：

```python
# 在 config/base_config.py 中
HEADLESS = False  # 顯示瀏覽器視窗
```

## 📞 技術支援

如果遇到問題：

1. 查看測試報告中的錯誤詳情
2. 檢查系統環境是否符合要求
3. 參考專案主 README 文件
4. 查看專案 Issues 頁面

## 🔄 更新日誌

- **v1.0.0**: 初始版本，支援7個平台的基礎測試
- **v1.1.0**: 新增互動式測試模式
- **v1.2.0**: 新增系統環境檢查功能
- **v1.3.0**: 新增測試報告查看功能

## 📝 注意事項

1. **合規使用**: 請遵守各平台的使用條款和 robots.txt 規則
2. **頻率控制**: 避免過於頻繁的請求，以免被平台限制
3. **數據用途**: 僅用於學習和研究目的，不得用於商業用途
4. **隱私保護**: 不要爬取和存儲個人隱私資訊

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request 來改進測試工具！
