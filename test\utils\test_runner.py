#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器
负责执行各种测试任务
"""

import asyncio
import sys
import os
import time
import subprocess
from typing import Dict, List, Optional, Any
from datetime import datetime
import threading
import queue

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    import config
    from media_platform.xhs import XiaoHongShuCrawler
    from media_platform.douyin import DouYinCrawler
    from media_platform.kuaishou import KuaishouCrawler
    from media_platform.bilibili import BilibiliCrawler
    from media_platform.weibo import WeiboCrawler
    from media_platform.tieba import TieBaCrawler
    from media_platform.zhihu import ZhihuCrawler
except ImportError as e:
    print(f"导入爬虫模块失败: {e}")
    # 创建模拟的爬虫类用于测试
    class MockCrawler:
        def __init__(self):
            pass
        def start(self):
            pass
        def search(self):
            pass
        def launch_browser(self):
            pass

    XiaoHongShuCrawler = MockCrawler
    DouYinCrawler = MockCrawler
    KuaishouCrawler = MockCrawler
    BilibiliCrawler = MockCrawler
    WeiboCrawler = MockCrawler
    TieBaCrawler = MockCrawler
    ZhihuCrawler = MockCrawler


class TestRunner:
    """测试运行器类"""
    
    def __init__(self):
        # 确保工作目录正确
        self.project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        self.platforms = {
            "xhs": {
                "name": "小红书",
                "crawler": XiaoHongShuCrawler,
                "test_keywords": ["美食", "旅行", "穿搭"],
                "icon": "📱"
            },
            "dy": {
                "name": "抖音",
                "crawler": DouYinCrawler,
                "test_keywords": ["搞笑", "音乐", "舞蹈"],
                "icon": "🎵"
            },
            "ks": {
                "name": "快手",
                "crawler": KuaishouCrawler,
                "test_keywords": ["美食", "生活", "搞笑"],
                "icon": "⚡"
            },
            "bili": {
                "name": "B站",
                "crawler": BilibiliCrawler,
                "test_keywords": ["动画", "游戏", "科技"],
                "icon": "📺"
            },
            "wb": {
                "name": "微博",
                "crawler": WeiboCrawler,
                "test_keywords": ["热搜", "新闻", "娱乐"],
                "icon": "🐦"
            },
            "tieba": {
                "name": "贴吧",
                "crawler": TieBaCrawler,
                "test_keywords": ["游戏", "动漫", "科技"],
                "icon": "💬"
            },
            "zhihu": {
                "name": "知乎",
                "crawler": ZhihuCrawler,
                "test_keywords": ["编程", "科技", "职场"],
                "icon": "🧠"
            }
        }
    
    def check_system_status(self) -> Dict[str, Any]:
        """检查系统状态"""
        status = {
            "python_ok": False,
            "python_version": "",
            "dependencies_ok": False,
            "deps_count": 0,
            "browser_ok": False,
            "browser_count": 0,
            "config_ok": False,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 检查 Python 版本
            version = sys.version_info
            status["python_version"] = f"{version.major}.{version.minor}.{version.micro}"
            status["python_ok"] = version >= (3, 9)
            
            # 检查依赖包
            try:
                import playwright
                import httpx
                import tenacity
                status["dependencies_ok"] = True
                status["deps_count"] = 3
            except ImportError:
                status["dependencies_ok"] = False
            
            # 检查配置文件
            config_files = [
                "config/base_config.py",
                "config/db_config.py"
            ]
            
            config_exists = all(
                os.path.exists(os.path.join(self.project_root, config_file))
                for config_file in config_files
            )
            status["config_ok"] = config_exists
            
            # 检查浏览器驱动（简化检查）
            try:
                # 这里简化处理，避免在 Streamlit 中使用同步 API
                status["browser_ok"] = True
                status["browser_count"] = 3  # 假设有 3 个浏览器驱动
            except Exception:
                status["browser_ok"] = False
                status["browser_count"] = 0
                
        except Exception as e:
            print(f"系统状态检查失败: {str(e)}")
        
        return status
    
    def get_platform_status(self) -> Dict[str, Dict]:
        """获取平台状态"""
        status = {}

        for platform_key, platform_info in self.platforms.items():
            try:
                # 切换到项目根目录
                original_cwd = os.getcwd()
                os.chdir(self.project_root)

                # 尝试创建爬虫实例
                crawler = platform_info["crawler"]()

                # 检查必要方法
                has_start = hasattr(crawler, 'start')
                has_search = hasattr(crawler, 'search')
                has_launch_browser = hasattr(crawler, 'launch_browser')

                available = has_start and has_search and has_launch_browser

                status[platform_key] = {
                    "available": available,
                    "last_test": "从未测试",
                    "status": "可用" if available else "不可用",
                    "methods": {
                        "start": has_start,
                        "search": has_search,
                        "launch_browser": has_launch_browser
                    }
                }

            except Exception as e:
                status[platform_key] = {
                    "available": False,
                    "last_test": "从未测试",
                    "status": f"错误: {str(e)}",
                    "methods": {
                        "start": False,
                        "search": False,
                        "launch_browser": False
                    }
                }
            finally:
                # 恢复原始工作目录
                try:
                    os.chdir(original_cwd)
                except:
                    pass

        return status
    
    async def test_platform_basic(self, platform_key: str, progress_callback=None) -> Dict:
        """测试单个平台的基本功能"""
        if progress_callback:
            progress_callback(f"开始测试 {self.platforms[platform_key]['name']}")
        
        platform = self.platforms[platform_key]
        result = {
            "platform": platform_key,
            "name": platform["name"],
            "status": "未测试",
            "error": None,
            "start_time": datetime.now(),
            "end_time": None,
            "duration": 0,
            "details": {}
        }
        
        try:
            if progress_callback:
                progress_callback(f"创建 {platform['name']} 爬虫实例...")
            
            # 创建爬虫实例
            crawler = platform["crawler"]()
            
            if progress_callback:
                progress_callback(f"检查 {platform['name']} 爬虫方法...")
            
            # 检查基本方法
            details = {
                "has_start": hasattr(crawler, 'start'),
                "has_search": hasattr(crawler, 'search'),
                "has_launch_browser": hasattr(crawler, 'launch_browser'),
                "instance_created": True
            }
            
            result["details"] = details
            
            if all(details.values()):
                result["status"] = "通过"
                if progress_callback:
                    progress_callback(f"✅ {platform['name']} 测试通过")
            else:
                result["status"] = "部分通过"
                if progress_callback:
                    progress_callback(f"⚠️ {platform['name']} 部分功能缺失")
            
        except Exception as e:
            result["status"] = "失败"
            result["error"] = str(e)
            if progress_callback:
                progress_callback(f"❌ {platform['name']} 测试失败: {str(e)}")
        
        finally:
            result["end_time"] = datetime.now()
            result["duration"] = (result["end_time"] - result["start_time"]).total_seconds()
        
        return result
    
    async def run_quick_test(self, selected_platforms: List[str], progress_callback=None) -> Dict:
        """运行快速测试"""
        if progress_callback:
            progress_callback("开始快速测试...")
        
        results = {
            "test_type": "quick_test",
            "start_time": datetime.now(),
            "end_time": None,
            "total_platforms": len(selected_platforms),
            "platforms": {},
            "summary": {
                "total": len(selected_platforms),
                "passed": 0,
                "failed": 0,
                "partial": 0
            }
        }
        
        for i, platform_key in enumerate(selected_platforms):
            if progress_callback:
                progress_callback(f"测试进度: {i+1}/{len(selected_platforms)}")
            
            result = await self.test_platform_basic(platform_key, progress_callback)
            results["platforms"][platform_key] = result
            
            # 更新统计
            if result["status"] == "通过":
                results["summary"]["passed"] += 1
            elif result["status"] == "失败":
                results["summary"]["failed"] += 1
            else:
                results["summary"]["partial"] += 1
            
            # 短暂延迟
            await asyncio.sleep(0.5)
        
        results["end_time"] = datetime.now()
        results["total_duration"] = (results["end_time"] - results["start_time"]).total_seconds()
        
        if progress_callback:
            progress_callback("快速测试完成！")
        
        return results
    
    def run_real_crawl_test(self, platform_key: str, config: Dict, progress_callback=None) -> Dict:
        """运行真实爬取测试"""
        if progress_callback:
            progress_callback(f"准备启动 {self.platforms[platform_key]['name']} 爬虫...")
        
        result = {
            "platform": platform_key,
            "name": self.platforms[platform_key]["name"],
            "status": "未开始",
            "start_time": datetime.now(),
            "end_time": None,
            "duration": 0,
            "output_lines": [],
            "error": None
        }
        
        try:
            # 构建命令
            cmd = [
                sys.executable, "main.py",
                "--platform", platform_key,
                "--lt", config.get("login_type", "qrcode"),
                "--type", config.get("crawl_type", "search"),
                "--keywords", config.get("keywords", "测试"),
                "--save_data_option", config.get("save_format", "json"),
                "--get_comment", str(config.get("get_comments", False)).lower()
            ]
            
            if progress_callback:
                progress_callback(f"执行命令: {' '.join(cmd)}")
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 监控输出
            timeout = config.get("timeout", 30)
            start_time = time.time()
            
            while True:
                if time.time() - start_time > timeout:
                    process.terminate()
                    result["status"] = "超时"
                    if progress_callback:
                        progress_callback("测试超时")
                    break
                
                line = process.stdout.readline()
                if not line:
                    if process.poll() is not None:
                        break
                    continue
                
                line = line.strip()
                if line:
                    result["output_lines"].append(line)
                    if progress_callback:
                        progress_callback(f"输出: {line}")
                    
                    # 检查关键输出
                    if "二维码" in line or "QR" in line:
                        result["status"] = "等待登录"
                    elif "Crawler finished" in line:
                        result["status"] = "完成"
                        break
                    elif "error" in line.lower():
                        result["status"] = "错误"
                        result["error"] = line
            
            # 清理进程
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=5)
            
        except Exception as e:
            result["status"] = "异常"
            result["error"] = str(e)
            if progress_callback:
                progress_callback(f"测试异常: {str(e)}")
        
        finally:
            result["end_time"] = datetime.now()
            result["duration"] = (result["end_time"] - result["start_time"]).total_seconds()
        
        return result
